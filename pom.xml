<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>me.zivush</groupId>
  <artifactId>GrimoireEnchant</artifactId>
  <version>1.0</version>
  <packaging>jar</packaging>

  <name>GrimoireEnchant</name>

  <properties>
    <java.version>21</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>

  <repositories>
      <repository>
          <id>spigotmc-repo</id>
          <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
      </repository>
      <repository>
          <id>sonatype</id>
          <url>https://oss.sonatype.org/content/groups/public/</url>
      </repository>
      <repository>
          <id>lumine-public</id>
          <name>Lumine Public Maven</name>
          <url>https://mvn.lumine.io/repository/maven-public/</url>
      </repository>
      <repository>
          <id>nightexpress-releases</id>
          <url>https://repo.nightexpressdev.com/releases</url>
      </repository>
      <repository>
          <id>fancyplugins-releases</id>
          <name>FancyPlugins Repository</name>
          <url>https://repo.fancyplugins.de/releases</url>
      </repository>
      <repository>
          <id>placeholderapi</id>
          <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
      </repository>
  </repositories>

  <dependencies>
      <dependency>
          <groupId>org.spigotmc</groupId>
          <artifactId>spigot-api</artifactId>
          <version>1.20-R0.1-SNAPSHOT</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>io.lumine</groupId>
          <artifactId>Mythic-Dist</artifactId>
          <version>5.6.1</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>su.nightexpress.coinsengine</groupId>
          <artifactId>CoinsEngine</artifactId>
          <version>2.4.2</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>de.oliver</groupId>
          <artifactId>FancyHolograms</artifactId>
          <version>2.4.2</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>me.clip</groupId>
          <artifactId>placeholderapi</artifactId>
          <version>2.11.5</version>
          <scope>provided</scope>
      </dependency>
  </dependencies>
</project>
