# GrimoireEnchant Messages Configuration
# Version 1.0
# Supports hex colors with &#RRGGBB format and standard Minecraft color codes (&a, &b, etc.)

# General Messages
General:
  Prefix: '&#8A2BE2[&#D15FEEɢʀɪᴍᴏɪʀᴇ&#8A2BE2] &r'
  NoPermission: '&#FF6347ʏᴏᴜ ᴅᴏ ɴᴏᴛ ʜᴀᴠᴇ ᴘᴇʀᴍɪssɪᴏɴ ᴛᴏ ᴜsᴇ ᴛʜɪs ᴄᴏᴍᴍᴀɴᴅ.'
  PlayerOnly: '&#FF6347ᴛʜɪs ᴄᴏᴍᴍᴀɴᴅ ᴄᴀɴ ᴏɴʟʏ ʙᴇ ᴇxᴇᴄᴜᴛᴇᴅ ʙʏ ᴀ ᴘʟᴀʏᴇʀ.'
  InvalidPlayer: '&#FF6347ᴘʟᴀʏᴇʀ &#FFCC33{player} &#FF6347ɴᴏᴛ ғᴏᴜɴᴅ.'
  InvalidEnchantment: '&#FF6347ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ &#FFCC33{enchant} &#FF6347ɴᴏᴛ ғᴏᴜɴᴅ.'
  InvalidAmount: '&#FF6347ᴀᴍᴏᴜɴᴛ ᴍᴜsᴛ ʙᴇ ᴀ ᴘᴏsɪᴛɪᴠᴇ ɴᴜᴍʙᴇʀ.'
  InvalidRarity: '&#FF6347ɪɴᴠᴀʟɪᴅ ʀᴀʀɪᴛʏ: &#FFCC33{rarity}&#FF6347. ᴠᴀʟɪᴅ ʀᴀʀɪᴛɪᴇs: ᴄᴏᴍᴍᴏɴ, ᴜɴᴄᴏᴍᴍᴏɴ, ʀᴀʀᴇ, ᴇᴘɪᴄ, ʟᴇɢᴇɴᴅᴀʀʏ.'
  ReloadSuccess: '&#4CBB17ᴄᴏɴғɪɢᴜʀᴀᴛɪᴏɴ ғɪʟᴇs ʜᴀᴠᴇ ʙᴇᴇɴ ʀᴇʟᴏᴀᴅᴇᴅ sᴜᴄᴄᴇssғᴜʟʟʏ.'
  ManaLow: '&#FF6347ɴᴏᴛ ᴇɴᴏᴜɢʜ ᴍᴀɴᴀ ᴛᴏ ᴜsᴇ ᴛʜɪs ᴀʙɪʟɪᴛʏ! &#A9A9A9(&#FFFFFF{current}&#A9A9A9/&#FFFFFF{required}&#A9A9A9)'

# Economy Messages
Economy:
  NotEnough: '&#FF6347ʏᴏᴜ ᴅᴏɴ'ᴛ ʜᴀᴠᴇ ᴇɴᴏᴜɢʜ {currency}. &#A9A9A9(&#FFFFFF{balance}&#A9A9A9/&#FFFFFF{cost}&#A9A9A9)'
  Success: '&#4CBB17ʏᴏᴜ ᴘᴀɪᴅ &#FFFFFF{cost} &#4CBB17{currency} ғᴏʀ ᴛʜᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ.'

# Command Messages
Command:
  Usage: '&#FF6347ᴜsᴀɢᴇ: /ɢᴇ ɢɪᴠᴇ <ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ> <ᴘʟᴀʏᴇʀ> [ʀᴀʀɪᴛʏ] [ᴀᴍᴏᴜɴᴛ]'
  CoreUsage: '&#FF6347ᴜsᴀɢᴇ: /ɢᴇ ᴄᴏʀᴇ ᴏᴘᴇɴ | /ɢᴇ ᴄᴏʀᴇ ɢɪᴠᴇ <ᴘʟᴀʏᴇʀ> <ᴄᴏʀᴇ>'
  CoreGiveUsage: '&#FF6347ᴜsᴀɢᴇ: /ɢᴇ ᴄᴏʀᴇ ɢɪᴠᴇ <ᴘʟᴀʏᴇʀ> <ᴄᴏʀᴇ>'
  GiveSuccess: '&#4CBB17ɢᴀᴠᴇ &#FFCC33{amount}x {enchant} &#4CBB17ᴛᴏ &#FFCC33{player}&#4CBB17.'
  ReceiveSuccess: '&#4CBB17ʏᴏᴜ ʀᴇᴄᴇɪᴠᴇᴅ &#FFCC33{amount}x {enchant}&#4CBB17.'

# Enchantment Messages
Enchantment:
  ApplySuccess: '&#4CBB17ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ ᴀᴘᴘʟɪᴇᴅ sᴜᴄᴄᴇssғᴜʟʟʏ!'
  ApplyCancel: '&#FF6347ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ ᴀᴘᴘʟɪᴄᴀᴛɪᴏɴ ᴄᴀɴᴄᴇʟʟᴇᴅ.'
  NoItem: '&#FF6347ʏᴏᴜ ᴍᴜsᴛ ʜᴏʟᴅ ᴀɴ ɪᴛᴇᴍ ᴛᴏ ᴀᴘᴘʟʏ ᴛʜᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ!'
  Cooldown: '&#FF6347ᴛʜɪs ᴀʙɪʟɪᴛʏ ɪs ᴏɴ ᴄᴏᴏʟᴅᴏᴡɴ ғᴏʀ &#FFCC33{time} &#FF6347sᴇᴄᴏɴᴅs.'
  AlreadyEnchanted: '&#FF6347ᴛʜɪs ɪᴛᴇᴍ ᴀʟʀᴇᴀᴅʏ ʜᴀs ᴀ ɢʀɪᴍᴏɪʀᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ!'
  InvalidItem: '&#FF6347ᴛʜɪs ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ ᴄᴀɴɴᴏᴛ ʙᴇ ᴀᴘᴘʟɪᴇᴅ ᴛᴏ ᴛʜɪs ɪᴛᴇᴍ!'
  InventoryOpen: '&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴀᴘᴘʟʏ ᴀ ɢʀɪᴍᴏɪʀᴇ ᴡʜɪʟᴇ ɪɴ ᴀɴᴏᴛʜᴇʀ ɪɴᴠᴇɴᴛᴏʀʏ!'
  CombineSuccess: '&#4CBB17ɢʀɪᴍᴏɪʀᴇs ᴄᴏᴍʙɪɴᴇᴅ sᴜᴄᴄᴇssғᴜʟʟʏ! ʏᴏᴜ ʀᴇᴄᴇɪᴠᴇᴅ ᴀ {new_rarity} ɢʀɪᴍᴏɪʀᴇ!'
  CombineCancel: '&#FF6347ɢʀɪᴍᴏɪʀᴇ ᴄᴏᴍʙɪɴᴀᴛɪᴏɴ ᴄᴀɴᴄᴇʟʟᴇᴅ.'
  IncompatibleGrimoires: '&#FF6347ᴛʜᴇsᴇ ɢʀɪᴍᴏɪʀᴇs ᴄᴀɴɴᴏᴛ ʙᴇ ᴄᴏᴍʙɪɴᴇᴅ. ᴛʜᴇʏ ᴍᴜsᴛ ʜᴀᴠᴇ ᴛʜᴇ sᴀᴍᴇ sᴘᴇʟʟ ᴀɴᴅ ʀᴀʀɪᴛʏ.'
  MaxRarity: '&#FF6347ᴛʜɪs ɢʀɪᴍᴏɪʀᴇ ɪs ᴀʟʀᴇᴀᴅʏ ᴀᴛ ᴛʜᴇ ʜɪɢʜᴇsᴛ ʀᴀʀɪᴛʏ ᴀɴᴅ ᴄᴀɴɴᴏᴛ ʙᴇ ᴜᴘɢʀᴀᴅᴇᴅ ғᴜʀᴛʜᴇʀ.'

# Specific Enchantment Messages
Summons:
  Spawn:
    Single: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ ᴀ ᴄᴏᴍᴘᴀɴɪᴏɴ ᴛᴏ ғɪɢʜᴛ ʙʏ ʏᴏᴜʀ sɪᴅᴇ!'
    Multiple: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ &#FFCC33{count} &#4CBB17ᴄᴏᴍᴘᴀɴɪᴏɴs ᴛᴏ ғɪɢʜᴛ ʙʏ ʏᴏᴜʀ sɪᴅᴇ!'
    Additional: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ &#FFCC33{count} &#4CBB17ᴍᴏʀᴇ ᴄᴏᴍᴘᴀɴɪᴏɴs! (&#FFCC33{total} &#4CBB17ᴛᴏᴛᴀʟ)'
  Despawn: '&#4CBB17ʏᴏᴜʀ ᴄᴏᴍᴘᴀɴɪᴏɴs ʜᴀᴠᴇ ʀᴇᴛᴜʀɴᴇᴅ ᴛᴏ ᴛʜᴇ sᴘɪʀɪᴛ ʀᴇᴀʟᴍ.'
  DespawnSingle: '&#4CBB17ᴏɴᴇ ᴏғ ʏᴏᴜʀ ᴄᴏᴍᴘᴀɴɪᴏɴs ʜᴀs ʀᴇᴛᴜʀɴᴇᴅ ᴛᴏ ᴛʜᴇ sᴘɪʀɪᴛ ʀᴇᴀʟᴍ.'
  DespawnMultiple: '&#4CBB17&#FFCC33{count} &#4CBB17ᴏғ ʏᴏᴜʀ ᴄᴏᴍᴘᴀɴɪᴏɴs ʜᴀᴠᴇ ʀᴇᴛᴜʀɴᴇᴅ ᴛᴏ ᴛʜᴇ sᴘɪʀɪᴛ ʀᴇᴀʟᴍ.'
  RemainingCount: ' (&#FFCC33{count} &#4CBB17ʀᴇᴍᴀɪɴɪɴɢ)'
  DespawnTeleport: '&#FF6347ʏᴏᴜʀ ᴄᴏᴍᴘᴀɴɪᴏɴs ᴄᴀɴɴᴏᴛ ғᴏʟʟᴏᴡ ʏᴏᴜ ᴛᴏ ᴀɴᴏᴛʜᴇʀ ᴡᴏʀʟᴅ.'

# Messages for Summons1 (example of a Summons variant)
# These messages will be used if specific messages for the variant are not found
Summons1:
  Spawn:
    Single: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ ᴀ sᴘᴇᴄɪᴀʟ ᴄᴏᴍᴘᴀɴɪᴏɴ ᴛᴏ ғɪɢʜᴛ ʙʏ ʏᴏᴜʀ sɪᴅᴇ!'
    Multiple: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ &#FFCC33{count} &#4CBB17sᴘᴇᴄɪᴀʟ ᴄᴏᴍᴘᴀɴɪᴏɴs ᴛᴏ ғɪɢʜᴛ ʙʏ ʏᴏᴜʀ sɪᴅᴇ!'
    Additional: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ sᴜᴍᴍᴏɴᴇᴅ &#FFCC33{count} &#4CBB17ᴍᴏʀᴇ sᴘᴇᴄɪᴀʟ ᴄᴏᴍᴘᴀɴɪᴏɴs! (&#FFCC33{total} &#4CBB17ᴛᴏᴛᴀʟ)'
  DespawnSingle: '&#4CBB17ᴏɴᴇ ᴏғ ʏᴏᴜʀ sᴘᴇᴄɪᴀʟ ᴄᴏᴍᴘᴀɴɪᴏɴs ʜᴀs ʀᴇᴛᴜʀɴᴇᴅ ᴛᴏ ᴛʜᴇ sᴘɪʀɪᴛ ʀᴇᴀʟᴍ.'
  DespawnMultiple: '&#4CBB17&#FFCC33{count} &#4CBB17ᴏғ ʏᴏᴜʀ sᴘᴇᴄɪᴀʟ ᴄᴏᴍᴘᴀɴɪᴏɴs ʜᴀᴠᴇ ʀᴇᴛᴜʀɴᴇᴅ ᴛᴏ ᴛʜᴇ sᴘɪʀɪᴛ ʀᴇᴀʟᴍ.'

ChainLightning:
  Activate: '&#00BFFFᴄʜᴀɪɴ ʟɪɢʜᴛɴɪɴɢ sᴛʀɪᴋᴇs ʏᴏᴜʀ ᴛᴀʀɢᴇᴛ!'
  Hit: '&#00BFFFʟɪɢʜᴛɴɪɴɢ ᴄʜᴀɪɴs ᴛᴏ &#FFCC33{count} &#00BFFFᴇɴᴛɪᴛɪᴇs!'

Raytrace:
  Activate: '&#00BFFFʏᴏᴜ ᴜɴʟᴇᴀsʜ ᴀ ʙᴀʀʀᴀɢᴇ ᴏғ ᴍᴀɢɪᴄᴀʟ ᴘʀᴏᴊᴇᴄᴛɪʟᴇs!'
  Hit: '&#00BFFFʏᴏᴜʀ sᴄᴀᴛᴛᴇʀ ʜɪᴛ &#FFCC33{count} &#00BFFFᴇɴᴛɪᴛɪᴇs!'

FlingSmash:
  Activate: '&#FF7F00ʏᴏᴜ ʟᴀᴜɴᴄʜ ɪɴᴛᴏ ᴛʜᴇ ᴀɪʀ, ᴘʀᴇᴘᴀʀɪɴɢ ᴛᴏ sᴍᴀsʜ ᴅᴏᴡɴ!'
  Hit: '&#FF7F00ʏᴏᴜʀ sᴍᴀsʜ ᴄʀᴇᴀᴛᴇs ᴀ sʜᴏᴄᴋᴡᴀᴠᴇ, ʜɪᴛᴛɪɴɢ &#FFCC33{count} &#FF7F00ᴇɴᴛɪᴛɪᴇs!'

PillarTurret:
  Activate: '&#8A2BE2ʏᴏᴜ sᴜᴍᴍᴏɴ ᴀ ᴍᴀɢɪᴄᴀʟ ᴛᴜʀʀᴇᴛ ᴛᴏ ᴀɪᴅ ʏᴏᴜ ɪɴ ʙᴀᴛᴛʟᴇ!'
  Hit: '&#8A2BE2ʏᴏᴜʀ ᴛᴜʀʀᴇᴛ ʜɪᴛ &#FFCC33{target} &#8A2BE2ғᴏʀ &#FFCC33{damage} &#8A2BE2ᴅᴀᴍᴀɢᴇ!'
  Expire: '&#8A2BE2ʏᴏᴜʀ ᴛᴜʀʀᴇᴛ ʜᴀs ᴅɪsᴀᴘᴘᴇᴀʀᴇᴅ.'
  OldRemoved: '&#8A2BE2ʏᴏᴜʀ ᴘʀᴇᴠɪᴏᴜs ᴛᴜʀʀᴇᴛ ʜᴀs ʙᴇᴇɴ ʀᴇᴘʟᴀᴄᴇᴅ.'
  InvalidLocation: '&#FF6347ᴄᴀɴɴᴏᴛ ᴘʟᴀᴄᴇ ᴀ ᴛᴜʀʀᴇᴛ ʜᴇʀᴇ. ɴᴇᴇᴅs ᴀ sᴏʟɪᴅ ʙʟᴏᴄᴋ ʙᴇʟᴏᴡ ᴀɴᴅ sᴘᴀᴄᴇ ᴀʙᴏᴠᴇ.'
  CannotBreak: '&#FF6347ᴛʜɪs ʙʟᴏᴄᴋ ɪs ᴘᴀʀᴛ ᴏғ ᴀ ᴍᴀɢɪᴄᴀʟ ᴛᴜʀʀᴇᴛ ᴀɴᴅ ᴄᴀɴɴᴏᴛ ʙᴇ ʙʀᴏᴋᴇɴ.'

RaytraceTeleport:
  Activate: '&#9370DBʏᴏᴜ ᴛᴇʟᴇᴘᴏʀᴛ ғᴏʀᴡᴀʀᴅ ᴛʜʀᴏᴜɢʜ sᴘᴀᴄᴇ!'
  Hit: '&#9370DBʏᴏᴜʀ ᴛᴇʟᴇᴘᴏʀᴛ ʜɪᴛ &#FFCC33{count} &#9370DBᴇɴᴛɪᴛɪᴇs!'
  Fail: '&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴛᴇʟᴇᴘᴏʀᴛ ᴛʜʀᴏᴜɢʜ sᴏʟɪᴅ ʙʟᴏᴄᴋs!'
  TeleportFail: '&#FF6347ᴛᴇʟᴇᴘᴏʀᴛᴀᴛɪᴏɴ ғᴀɪʟᴇᴅ!'
  NoDestination: '&#FF6347ɴᴏ ᴠᴀʟɪᴅ ᴛᴇʟᴇᴘᴏʀᴛ ᴅᴇsᴛɪɴᴀᴛɪᴏɴ ғᴏᴜɴᴅ!'

# Mana Core Messages
ManaCores:
  Equipped: '&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ ᴇǫᴜɪᴘᴘᴇᴅ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ.'
  Unequipped: '&#FF6347ʏᴏᴜ ʜᴀᴠᴇ ᴜɴᴇǫᴜɪᴘᴘᴇᴅ ʏᴏᴜʀ ᴍᴀɴᴀ ᴄᴏʀᴇ.'
  InvalidCore: '&#FF6347ᴍᴀɴᴀ ᴄᴏʀᴇ &#FFCC33{core} &#FF6347ɴᴏᴛ ғᴏᴜɴᴅ.'
  AlreadyHasCore: '&#FF6347ᴘʟᴀʏᴇʀ &#FFCC33{player} &#FF6347ᴀʟʀᴇᴀᴅʏ ʜᴀs ᴛʜᴇ &#FFCC33{core} &#FF6347ᴍᴀɴᴀ ᴄᴏʀᴇ.'
  GiveSuccess: '&#4CBB17ɢᴀᴠᴇ &#FFCC33{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ ᴛᴏ &#FFCC33{player}&#4CBB17.'
  ReceiveSuccess: '&#4CBB17ʏᴏᴜ ʀᴇᴄᴇɪᴠᴇᴅ ᴛʜᴇ &#FFCC33{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ.'
  TookBack: '&#4CBB17ʏᴏᴜ ᴛᴏᴏᴋ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ ʙᴀᴄᴋ ᴛᴏ ʏᴏᴜʀ ɪɴᴠᴇɴᴛᴏʀʏ.'
  InventoryFull: '&#FF6347ʏᴏᴜʀ ɪɴᴠᴇɴᴛᴏʀʏ ɪs ғᴜʟʟ! ᴛʜᴇ ᴄᴏʀᴇ ᴡᴀs ᴅʀᴏᴘᴘᴇᴅ ᴏɴ ᴛʜᴇ ɢʀᴏᴜɴᴅ.'
  CannotPlace: '&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴘʟᴀᴄᴇ ᴍᴀɴᴀ ᴄᴏʀᴇs ᴀs ʙʟᴏᴄᴋs!'
  IncompatibleRarity: '&#FF6347ʏᴏᴜʀ ᴄᴜʀʀᴇɴᴛ ᴍᴀɴᴀ ᴄᴏʀᴇ ᴄᴀɴɴᴏᴛ ᴜsᴇ &#FFFFFF{rarity} &#FF6347ɢʀɪᴍᴏɪʀᴇs.'
