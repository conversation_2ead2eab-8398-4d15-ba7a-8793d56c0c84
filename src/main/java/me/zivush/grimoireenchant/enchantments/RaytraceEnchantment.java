package me.zivush.grimoireenchant.enchantments;

import me.zivush.grimoireenchant.GrimoireEnchant;
import me.zivush.grimoireenchant.GrimoireEnchantment;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * Raytrace (Scatter) enchantment implementation
 */
public class RaytraceEnchantment extends GrimoireEnchantment implements Listener {

    private final Random random = new Random();

    // Store knockback data for entities that should receive knockback when damaged
    private final Map<UUID, Vector> pendingKnockback = new HashMap<>();

    /**
     * Constructor for RaytraceEnchantment
     *
     * @param plugin The main plugin instance
     */
    public RaytraceEnchantment(GrimoireEnchant plugin) {
        super(plugin, "Raytrace");

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    @Override
    public void activate(Player player, ItemStack item, String rarity) {
        // Check cooldown and mana
        if (!canUse(player, rarity)) {
            return;
        }

        // Get configuration for this rarity
        ConfigurationSection config = plugin.getConfig().getConfigurationSection("Abilities.Raytrace.Rarity." + rarity);
        if (config == null) {
            return;
        }

        int rayCount = config.getInt("Ray-Count", 3);
        double rayDistance = config.getDouble("Ray-Distance", 8.0);
        double rayDamage = config.getDouble("Ray-Damage", 10.0);
        double rayKnockback = config.getDouble("Ray-Knockback", 0.5);
        String particleName = config.getString("Ray-Particle", "SMOKE_NORMAL");
        int particleCount = config.getInt("Ray-Particle-Count", 10);
        int manaCost = config.getInt("Mana-Cost", 20);

        // Check if there are any entities nearby that could be hit
        boolean entitiesNearby = false;
        for (Entity entity : player.getNearbyEntities(rayDistance, rayDistance, rayDistance)) {
            if (entity instanceof LivingEntity && entity != player) {
                entitiesNearby = true;
                break;
            }
        }

        // If no entities nearby, don't consume mana or play sounds
        if (!entitiesNearby) {
            return;
        }

        // Use mana
        if (!plugin.getEnchantmentManager().useMana(player, manaCost)) {
            return;
        }

        // Set cooldown
        setCooldown(player);

        // Play trigger sound
        playTriggerSound(player);

        // Get the particle type
        Particle particle;
        try {
            particle = Particle.valueOf(particleName);
        } catch (IllegalArgumentException e) {
            particle = Particle.SMOKE_NORMAL;
        }

        // Send activation message
        String activateMsg = ColorUtils.process(
                plugin.getMessagesConfig().getString("Raytrace.Activate", "&bYou unleash a barrage of magical projectiles!"));
        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + activateMsg);

        // Track hit entities and count hits per entity
        Map<Entity, Integer> hitEntities = new HashMap<>();
        int totalHits = 0;

        // Fire rays
        for (int i = 0; i < rayCount; i++) {
            // Get a random direction with more spread (shotgun-like)
            Vector direction = getRandomDirection(player.getLocation().getDirection(), 0.5);

            // Perform raycast
            Entity hit = raycast(player, direction, rayDistance, particle, particleCount);

            // If hit an entity, track it for damage calculation
            if (hit != null && hit instanceof LivingEntity) {
                LivingEntity target = (LivingEntity) hit;

                // Count hits for this entity
                int hits = hitEntities.getOrDefault(hit, 0) + 1;

                // Store the direction of the first hit for knockback
                if (hits == 1) {
                    // Store the entity and the direction for later knockback application
                    hitEntities.put(hit, hits);

                    // Store the knockback direction for this entity
                    Vector knockbackVector = direction.clone().multiply(rayKnockback);
                    pendingKnockback.put(target.getUniqueId(), knockbackVector);
                } else {
                    // Just update the hit count for existing entities
                    hitEntities.put(hit, hits);
                }

                totalHits++;

                // Play hit sound
                playHitSound(player);
            }
        }

        // Apply damage and send hit message
        if (totalHits > 0) {
            int entitiesHit = hitEntities.size();

            // Apply accumulated damage to each entity
            for (Map.Entry<Entity, Integer> entry : hitEntities.entrySet()) {
                if (entry.getKey() instanceof LivingEntity) {
                    LivingEntity entity = (LivingEntity) entry.getKey();
                    int hits = entry.getValue();
                    double totalDamage = rayDamage * hits;

                    // Apply the total damage at once
                    entity.damage(totalDamage, player);

                    // Debug message
                    if (plugin.getConfig().getBoolean("Debug", false)) {
                        player.sendMessage(ColorUtils.process("&7Debug: &f" + entity.getName() + " &7was hit &f" + hits + " &7times for &f" +
                                totalDamage + " &7damage"));
                    }
                }
            }

            // Send hit message
            String hitMsg = ColorUtils.process(
                    plugin.getMessagesConfig().getString("Raytrace.Hit", "&bYour scatter hit &e{count} &bentities with &e{rays} &brays!")
                            .replace("{count}", String.valueOf(entitiesHit))
                            .replace("{rays}", String.valueOf(totalHits)));
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + hitMsg);
        }

        // Clean up old pending knockback data
        cleanupPendingKnockback();
    }

    /**
     * Get a random direction with some spread
     *
     * @param direction The base direction
     * @param spread The amount of spread
     * @return A random direction vector
     */
    private Vector getRandomDirection(Vector direction, double spread) {
        // Create a random vector with the given spread
        Vector randomVector = new Vector(
                (random.nextDouble() - 0.5) * spread,
                (random.nextDouble() - 0.5) * spread,
                (random.nextDouble() - 0.5) * spread
        );

        // Add the random vector to the direction and normalize
        return direction.clone().add(randomVector).normalize();
    }

    /**
     * Perform a raycast from the player's location
     *
     * @param player The player
     * @param direction The direction to raycast
     * @param maxDistance The maximum distance to raycast
     * @param particle The particle to display
     * @param particleCount The number of particles to display
     * @return The entity hit, or null if none
     */
    private Entity raycast(Player player, Vector direction, double maxDistance, Particle particle, int particleCount) {
        World world = player.getWorld();
        Location start = player.getEyeLocation();
        Vector step = direction.clone().multiply(0.5); // Step size of 0.5 blocks

        // Calculate the number of steps
        int steps = (int) Math.ceil(maxDistance / 0.5);

        // Track the current position
        Location current = start.clone();

        // Perform the raycast
        for (int i = 0; i < steps; i++) {
            // Move the current position
            current.add(step);

            // Display particles
            if (i % (steps / particleCount) == 0) {
                world.spawnParticle(particle, current, 1, 0, 0, 0, 0);
            }

            // Check for entities at the current position
            for (Entity entity : world.getNearbyEntities(current, 0.5, 0.5, 0.5)) {
                if (entity != player && entity instanceof LivingEntity) {
                    return entity;
                }
            }

            // Check if we hit a solid block
            if (!current.getBlock().isPassable()) {
                return null;
            }
        }

        return null;
    }

    /**
     * Handle entity damage events to apply knockback only when damage is actually dealt
     */
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damager is a player and the entity has pending knockback
        if (event.getDamager() instanceof Player && event.getEntity() instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity) event.getEntity();
            UUID entityId = entity.getUniqueId();

            // Check if this entity has pending knockback from our enchantment
            if (pendingKnockback.containsKey(entityId)) {
                // Schedule knockback application for next tick (after damage is processed)
                Vector knockbackVector = pendingKnockback.get(entityId);

                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // Only apply knockback if the damage event wasn't cancelled
                        if (!event.isCancelled() && entity.isValid()) {
                            entity.setVelocity(entity.getVelocity().add(knockbackVector));
                        }
                        // Remove the pending knockback regardless
                        pendingKnockback.remove(entityId);
                    }
                }.runTaskLater(plugin, 1L);
            }
        }
    }

    /**
     * Clean up old pending knockback data to prevent memory leaks
     */
    private void cleanupPendingKnockback() {
        // Schedule cleanup of pending knockback data after 5 seconds
        new BukkitRunnable() {
            @Override
            public void run() {
                pendingKnockback.clear();
            }
        }.runTaskLater(plugin, 100L); // 5 seconds
    }
}
