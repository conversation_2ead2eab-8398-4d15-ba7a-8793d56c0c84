package me.zivush.grimoireenchant.enchantments;

import de.oliver.fancyholograms.api.FancyHologramsPlugin;
import de.oliver.fancyholograms.api.HologramManager;
import de.oliver.fancyholograms.api.data.TextHologramData;
import de.oliver.fancyholograms.api.hologram.Hologram;
import org.bukkit.entity.Display;
import org.bukkit.entity.TextDisplay;
import org.joml.Vector3f;
import me.zivush.grimoireenchant.GrimoireEnchant;
import me.zivush.grimoireenchant.enchantments.SummonsEnchantment;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.profile.PlayerProfile;
import org.bukkit.profile.PlayerTextures;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.EulerAngle;
import org.bukkit.util.Vector;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.UUID;

/**
 * Class representing a turret entity that tracks and shoots at nearby enemies
 */
public class TurretEntity {
    private final GrimoireEnchant plugin;
    private final UUID ownerUuid;
    private final String ownerName;
    private final String rarity;
    private final Location location;
    private final double range;
    private final double damage;
    private final int shootInterval;
    private final Particle shootParticle;
    private final int shootParticleCount;
    private final int duration;
    private final String headTexture;

    // Hologram configuration
    private final String hologramFormat;
    private final double hologramYOffset;
    private final String hologramColor;
    private final TextDisplay.TextAlignment textAlignment;
    private final String backgroundColor;
    private final float backgroundOpacity;
    private final boolean shadow;
    private final boolean seeThrough;
    private final Display.Billboard billboard;
    private final float scale;
    private final int lineWidth;
    private final boolean defaultBackground;

    private ArmorStand armorStand;
    private Hologram durationHologram;
    private String hologramId;
    private BukkitTask shootTask;
    private BukkitTask removeTask;
    private BukkitTask countdownTask;
    private int remainingSeconds;
    private boolean isActive = false; // Flag to track if turret is currently shooting
    private long lastShotTime = 0; // Time when the turret last shot

    /**
     * Constructor for TurretEntity
     *
     * @param plugin The main plugin instance
     * @param owner The player who owns the turret
     * @param location The location to spawn the turret
     * @param range The range of the turret
     * @param damage The damage per shot
     * @param shootInterval The interval between shots in ticks
     * @param shootParticle The particle to display when shooting
     * @param shootParticleCount The number of particles to display when shooting
     * @param duration The duration of the turret in seconds
     * @param rarity The rarity of the enchantment
     * @param headTexture The texture value for the head (can be null to use owner's head)
     * @param hologramFormat The format string for the hologram text
     * @param hologramYOffset The Y offset for the hologram position
     * @param hologramColor The color for the hologram text
     * @param textAlignment The text alignment for the hologram
     * @param backgroundColor The background color for the hologram
     * @param backgroundOpacity The background opacity for the hologram
     * @param shadow Whether to show text shadow
     * @param seeThrough Whether the hologram can be seen through blocks
     * @param billboard The billboard mode for the hologram
     * @param scale The scale of the hologram
     * @param lineWidth The maximum width of a line in pixels before wrapping
     * @param defaultBackground Whether to use the default background
     */
    public TurretEntity(GrimoireEnchant plugin, Player owner, Location location, double range, double damage,
                        int shootInterval, Particle shootParticle, int shootParticleCount, int duration,
                        String rarity, String headTexture, String hologramFormat, double hologramYOffset,
                        String hologramColor, TextDisplay.TextAlignment textAlignment, String backgroundColor,
                        float backgroundOpacity, boolean shadow, boolean seeThrough, Display.Billboard billboard,
                        float scale, int lineWidth, boolean defaultBackground) {
        this.plugin = plugin;
        this.ownerUuid = owner.getUniqueId();
        this.ownerName = owner.getName();
        this.location = location.clone();
        this.range = range;
        this.damage = damage;
        this.shootInterval = shootInterval;
        this.shootParticle = shootParticle;
        this.shootParticleCount = shootParticleCount;
        this.duration = duration;
        this.rarity = rarity;
        this.headTexture = headTexture;
        this.hologramFormat = hologramFormat;
        this.hologramYOffset = hologramYOffset;
        this.hologramColor = hologramColor;
        this.textAlignment = textAlignment;
        this.backgroundColor = backgroundColor;
        this.backgroundOpacity = backgroundOpacity;
        this.shadow = shadow;
        this.seeThrough = seeThrough;
        this.billboard = billboard;
        this.scale = scale;
        this.lineWidth = lineWidth;
        this.defaultBackground = defaultBackground;
        this.remainingSeconds = duration;
    }

    /**
     * Constructor for TurretEntity with basic parameters (for backward compatibility)
     *
     * @param plugin The main plugin instance
     * @param owner The player who owns the turret
     * @param location The location to spawn the turret
     * @param range The range of the turret
     * @param damage The damage per shot
     * @param shootInterval The interval between shots in ticks
     * @param shootParticle The particle to display when shooting
     * @param shootParticleCount The number of particles to display when shooting
     * @param duration The duration of the turret in seconds
     * @param rarity The rarity of the enchantment
     * @param headTexture The texture value for the head (can be null to use owner's head)
     * @param hologramFormat The format string for the hologram text
     * @param hologramYOffset The Y offset for the hologram position
     * @param hologramColor The color for the hologram text
     */
    public TurretEntity(GrimoireEnchant plugin, Player owner, Location location, double range, double damage,
                        int shootInterval, Particle shootParticle, int shootParticleCount, int duration,
                        String rarity, String headTexture, String hologramFormat, double hologramYOffset,
                        String hologramColor) {
        this(plugin, owner, location, range, damage, shootInterval, shootParticle, shootParticleCount, duration,
             rarity, headTexture, hologramFormat, hologramYOffset, hologramColor,
             TextDisplay.TextAlignment.CENTER, "#000000", 0.5f, true, false,
             Display.Billboard.CENTER, 1.0f, 200, false);
    }

    /**
     * Spawn the turret entity
     *
     * @return True if the turret was spawned successfully
     */
    public boolean spawn() {
        World world = location.getWorld();
        if (world == null) return false;

        // Create the armor stand with precise positioning
        armorStand = (ArmorStand) world.spawnEntity(location, EntityType.ARMOR_STAND);

        // Configure the armor stand
        armorStand.setVisible(false);
        armorStand.setGravity(false);
        armorStand.setInvulnerable(true);
        armorStand.setCustomName("TurretEntity");
        armorStand.setCustomNameVisible(false);
        armorStand.setSmall(true);
        armorStand.setMarker(true); // Makes it have no hitbox
        armorStand.setBasePlate(false);
        armorStand.setArms(false);

        // Set the player head
        ItemStack skull = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) skull.getItemMeta();
        if (meta != null) {
            // If a texture is provided, use it, otherwise use the owner's head
            if (headTexture != null && !headTexture.isEmpty()) {
                try {
                    // Create a new player profile with a random UUID
                    PlayerProfile profile = Bukkit.createPlayerProfile(UUID.randomUUID());

                    // Get the textures from the profile
                    PlayerTextures textures = profile.getTextures();

                    // The headTexture value is expected to be the base64 texture value from minecraft-heads.com
                    // We need to extract the URL from it
                    String decoded = new String(Base64.getDecoder().decode(headTexture));

                    // Extract the URL from the decoded JSON
                    // The format is typically: {"textures":{"SKIN":{"url":"http://textures.minecraft.net/texture/HASH"}}}
                    String urlPart = "url\":\"";
                    int urlIndex = decoded.indexOf(urlPart);
                    if (urlIndex != -1) {
                        int startIndex = urlIndex + urlPart.length();
                        int endIndex = decoded.indexOf("\"", startIndex);
                        String textureUrl = decoded.substring(startIndex, endIndex);

                        // Set the skin texture
                        textures.setSkin(new URL(textureUrl));

                        // Set the textures back to the profile
                        profile.setTextures(textures);

                        // Set the profile on the skull
                        meta.setOwnerProfile(profile);

                        plugin.getLogger().info("Applied custom head texture using Bukkit API: " + textureUrl);
                    } else {
                        // Try direct URL approach if the texture is a direct hash
                        try {
                            URL textureUrl = URI.create("https://textures.minecraft.net/texture/" + headTexture).toURL();
                            textures.setSkin(textureUrl);
                            profile.setTextures(textures);
                            meta.setOwnerProfile(profile);
                            plugin.getLogger().info("Applied custom head texture using direct URL: " + textureUrl);
                        } catch (Exception e2) {
                            throw new Exception("Could not parse texture value: " + e2.getMessage());
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to apply head texture: " + e.getMessage());

                    // Fallback to owner's head
                    Player owner = plugin.getServer().getPlayer(ownerUuid);
                    if (owner != null) {
                        meta.setOwningPlayer(owner);
                    }
                }
            } else {
                // Use the owner's head
                Player owner = plugin.getServer().getPlayer(ownerUuid);
                if (owner != null) {
                    meta.setOwningPlayer(owner);
                }
            }
            skull.setItemMeta(meta);
        }
        armorStand.getEquipment().setHelmet(skull);

        // Create hologram for duration display
        createDurationHologram();

        // Start tracking and shooting task
        startTasks();

        return true;
    }

    /**
     * Create the hologram that displays the remaining duration
     */
    private void createDurationHologram() {
        try {
            // Check if FancyHolograms plugin is available
            if (plugin.getServer().getPluginManager().getPlugin("FancyHolograms") != null) {
                HologramManager hologramManager = FancyHologramsPlugin.get().getHologramManager();

                // Create a unique ID for this hologram
                hologramId = "turret_" + ownerUuid.toString().substring(0, 8) + "_" + System.currentTimeMillis();

                // Create hologram data with the configured Y offset
                TextHologramData hologramData = new TextHologramData(hologramId, location.clone().add(0, hologramYOffset, 0));

                // Format the hologram text with placeholders
                String formattedText = formatHologramText();

                // Set the text
                hologramData.setText(Collections.singletonList(formattedText));

                // Set hologram to not be persistent (won't be saved)
                hologramData.setPersistent(false);

                // Apply advanced hologram options
                hologramData.setTextAlignment(textAlignment);

                // Apply background color if not using default background
                if (!defaultBackground) {
                    // Parse the hex color to RGB values
                    if (backgroundColor != null && backgroundColor.startsWith("#")) {
                        try {
                            String colorHex = backgroundColor.substring(1); // Remove the # prefix
                            int r = Integer.parseInt(colorHex.substring(0, 2), 16);
                            int g = Integer.parseInt(colorHex.substring(2, 4), 16);
                            int b = Integer.parseInt(colorHex.substring(4, 6), 16);

                            // Set background color - using the available API method
                            // Try to set background color with opacity if the method exists
                            try {
                                java.lang.reflect.Method setBackgroundColorMethod =
                                    TextHologramData.class.getMethod("setBackgroundColor", int.class, int.class, int.class, float.class);
                                setBackgroundColorMethod.invoke(hologramData, r, g, b, backgroundOpacity);
                            } catch (NoSuchMethodException e) {
                                // Fall back to the method without opacity
                                try {
                                    java.lang.reflect.Method setBackgroundColorMethod =
                                        TextHologramData.class.getMethod("setBackgroundColor", int.class, int.class, int.class);
                                    setBackgroundColorMethod.invoke(hologramData, r, g, b);
                                } catch (Exception ex) {
                                    plugin.getLogger().warning("Failed to set background color: " + ex.getMessage());
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("Failed to set background color with opacity: " + e.getMessage());
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("Failed to parse background color: " + backgroundColor);
                        }
                    }
                }

                // Set shadow - using the available API method
                // Note: If setShadow is not available, we'll use the appropriate method or skip
                try {
                    // This is a reflection-based approach since the method might not exist
                    java.lang.reflect.Method setShadowMethod = TextHologramData.class.getMethod("setShadow", boolean.class);
                    setShadowMethod.invoke(hologramData, shadow);
                } catch (Exception e) {
                    // Method doesn't exist, log it but continue
                    plugin.getLogger().info("setShadow method not available in this version of FancyHolograms");
                }

                // Set see-through
                hologramData.setSeeThrough(seeThrough);

                // Set billboard mode
                hologramData.setBillboard(billboard);

                // Set scale - using Vector3f as required by the API
                hologramData.setScale(new Vector3f(scale, scale, scale));

                // Set line width - using the available API method or skip
                try {
                    // This is a reflection-based approach since the method might not exist
                    java.lang.reflect.Method setLineWidthMethod = TextHologramData.class.getMethod("setLineWidth", int.class);
                    setLineWidthMethod.invoke(hologramData, lineWidth);
                } catch (Exception e) {
                    // Method doesn't exist, log it but continue
                    plugin.getLogger().info("setLineWidth method not available in this version of FancyHolograms");
                }

                // Create and add the hologram
                durationHologram = hologramManager.create(hologramData);
                hologramManager.addHologram(durationHologram);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create duration hologram: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Format the hologram text with placeholders
     *
     * @return The formatted text
     */
    private String formatHologramText() {
        // Get rarity-specific color
        String rarityColor = getRarityColor(rarity);

        // Get time color based on remaining duration
        String timeColor = getTimeColor();

        // Check if turret is active (recently shot)
        boolean currentlyActive = isActive && (System.currentTimeMillis() - lastShotTime < 2000); // Active for 2 seconds after shooting

        // Reset active flag if it's been more than 2 seconds since last shot
        if (isActive && !currentlyActive) {
            isActive = false;
        }

        // Add visual indicator for active state
        String activePrefix = "";
        if (currentlyActive) {
            // Pulsing effect based on current time
            long pulseTime = System.currentTimeMillis() % 1000; // 0-999 milliseconds
            if (pulseTime < 500) {
                activePrefix = "&#FF0000⚡ &#FFFF00ᴀᴄᴛɪᴠᴇ &#FF0000⚡ \n";
            } else {
                activePrefix = "&#FFFF00⚡ &#FF0000ᴀᴄᴛɪᴠᴇ &#FFFF00⚡ \n";
            }
        }

        // Replace placeholders in the format string
        String text = activePrefix + hologramFormat
            .replace("%time_left%", timeColor + String.valueOf(remainingSeconds))
            .replace("%owner%", ownerName)
            .replace("%rarity%", rarityColor + rarity);

        // Apply base color
        return ColorUtils.process(hologramColor + text);
    }

    /**
     * Get color code for a specific rarity
     *
     * @param rarity The rarity to get color for
     * @return The color code for the rarity
     */
    private String getRarityColor(String rarity) {
        switch (rarity.toLowerCase()) {
            case "common":
                return "&#AAAAAA"; // Gray
            case "uncommon":
                return "&#55FF55"; // Green
            case "rare":
                return "&#5555FF"; // Blue
            case "epic":
                return "&#AA00AA"; // Purple
            case "legendary":
                return "&#FFAA00"; // Gold
            default:
                return "&#FFFFFF"; // White
        }
    }

    /**
     * Get color code for the remaining time based on percentage of time left
     *
     * @return The color code for the time display
     */
    private String getTimeColor() {
        // Calculate percentage of time remaining
        double percentRemaining = (double) remainingSeconds / duration;

        if (percentRemaining > 0.66) {
            return "&#00FF00"; // Green for > 66% time remaining
        } else if (percentRemaining > 0.33) {
            return "&#FFFF00"; // Yellow for 33-66% time remaining
        } else if (percentRemaining > 0.15) {
            return "&#FFA500"; // Orange for 15-33% time remaining
        } else {
            return "&#FF0000"; // Red for < 15% time remaining
        }
    }

    /**
     * Update the duration hologram text
     */
    private void updateDurationHologram() {
        try {
            if (durationHologram != null && durationHologram.getData() instanceof TextHologramData) {
                TextHologramData textData = (TextHologramData) durationHologram.getData();

                // Format the hologram text with updated time
                String formattedText = formatHologramText();

                // Update the text
                textData.setText(Collections.singletonList(formattedText));
                durationHologram.forceUpdate();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to update duration hologram: " + e.getMessage());
        }
    }

    /**
     * Start the tasks for tracking, shooting, and removing the turret
     */
    private void startTasks() {
        // Task to shoot at nearby enemies
        shootTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (armorStand == null || armorStand.isDead()) {
                    this.cancel();
                    return;
                }

                // Find the nearest enemy within range
                LivingEntity target = findNearestEnemy();
                if (target != null) {
                    // Look at the target
                    lookAtEntity(target);

                    // Shoot at the target
                    shootAtTarget(target);
                }
            }
        }.runTaskTimer(plugin, shootInterval, shootInterval);

        // Task to remove the turret after the duration
        removeTask = new BukkitRunnable() {
            @Override
            public void run() {
                remove();

                // Send expiry message to the owner
                Player owner = plugin.getServer().getPlayer(ownerUuid);
                if (owner != null) {
                    String expiryMsg = ColorUtils.process(
                            plugin.getMessagesConfig().getString("PillarTurret.Expire", "&#8A2BE2ʏᴏᴜʀ ᴛᴜʀʀᴇᴛ ʜᴀs ᴅɪsᴀᴘᴘᴇᴀʀᴇᴅ."));
                    owner.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "")) + expiryMsg);
                }
            }
        }.runTaskLater(plugin, duration * 20L);

        // Task to update the countdown timer and hologram
        countdownTask = new BukkitRunnable() {
            @Override
            public void run() {
                remainingSeconds--;
                updateDurationHologram();

                if (remainingSeconds <= 0) {
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 20L, 20L);

        // Additional task to update the hologram more frequently when active (for pulsing effect)
        new BukkitRunnable() {
            @Override
            public void run() {
                // Only update if the turret is active and not removed
                if (isActive && armorStand != null && !armorStand.isDead()) {
                    updateDurationHologram();
                }

                // Cancel if the turret is removed
                if (armorStand == null || armorStand.isDead()) {
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 5L, 5L); // Update every 5 ticks (1/4 second) for smooth pulsing
    }

    /**
     * Find the nearest enemy to the turret within range that has a clear line of sight
     *
     * @return The nearest enemy, or null if none found
     */
    private LivingEntity findNearestEnemy() {
        if (armorStand == null || armorStand.getWorld() == null) return null;

        // Get the player who owns the turret
        Player owner = plugin.getServer().getPlayer(ownerUuid);
        if (owner == null) return null;

        Location turretLoc = armorStand.getLocation();
        Collection<Entity> nearbyEntities = armorStand.getWorld().getNearbyEntities(
                turretLoc, range, range, range);

        return nearbyEntities.stream()
                .filter(entity -> entity instanceof LivingEntity)
                .filter(entity -> !(entity instanceof ArmorStand)) // Exclude armor stands
                .filter(entity -> entity.getUniqueId() != armorStand.getUniqueId())
                .filter(entity -> entity.getUniqueId() != ownerUuid) // Exclude owner
                .filter(entity -> !(entity instanceof Player)) // Exclude players
                .filter(entity -> !SummonsEnchantment.isSummoned(entity)) // Exclude summoned entities
                .map(entity -> (LivingEntity) entity)
                // Only target entities that have a clear line of sight
                .filter(this::hasLineOfSight)
                .min(Comparator.comparingDouble(entity -> entity.getLocation().distanceSquared(turretLoc)))
                .orElse(null);
    }

    /**
     * Check if the turret has a clear line of sight to the target entity
     *
     * @param target The target entity to check
     * @return True if there is a clear line of sight, false otherwise
     */
    private boolean hasLineOfSight(LivingEntity target) {
        if (armorStand == null || armorStand.getWorld() == null) return false;

        // Get the turret's head position (where projectiles come from)
        Location turretHead = armorStand.getLocation().clone().add(0, 1, 0);

        // Get the target's center position
        Location targetCenter = target.getLocation().add(0, target.getHeight() / 2, 0);

        // Calculate the direction vector from the turret to the target
        Vector direction = targetCenter.clone().subtract(turretHead).toVector();
        double distance = direction.length();
        direction.normalize();

        // Perform a ray trace to check for blocks in the way
        for (double d = 0.5; d < distance; d += 0.5) {
            Location checkLoc = turretHead.clone().add(direction.clone().multiply(d));

            // If the block at this location is not passable, there's no line of sight
            if (!checkLoc.getBlock().isPassable()) {
                return false;
            }
        }

        // No blocks in the way, we have line of sight
        return true;
    }

    /**
     * Make the turret look at the target entity
     *
     * @param target The target entity to look at
     */
    private void lookAtEntity(Entity target) {
        if (armorStand == null) return;

        Location headLocation = armorStand.getLocation();
        Location targetLocation = target.getLocation().add(0, target.getHeight() / 2, 0);

        // Calculate the direction vector from the head to the target
        Vector direction = targetLocation.toVector().subtract(headLocation.toVector());

        // Set the head's rotation
        Location newLocation = headLocation.clone();
        newLocation.setDirection(direction);

        // Calculate pitch in radians for the head pose
        double pitch = Math.toRadians(newLocation.getPitch());
        double yaw = Math.toRadians(newLocation.getYaw());

        // First teleport the armor stand to update its yaw (body rotation)
        armorStand.teleport(newLocation);

        // Then set the head pose for vertical tracking (pitch)
        armorStand.setHeadPose(new EulerAngle(pitch, 0, 0));
    }

    /**
     * Shoot at a target from the turret
     *
     * @param target The target to shoot at
     */
    private void shootAtTarget(LivingEntity target) {
        World world = armorStand.getWorld();
        if (world == null) return;

        // Get the player who owns the turret
        Player owner = plugin.getServer().getPlayer(ownerUuid);
        if (owner == null) return;

        // Double-check line of sight before shooting
        if (!hasLineOfSight(target)) {
            return;
        }

        // Set active flag and update last shot time
        isActive = true;
        lastShotTime = System.currentTimeMillis();

        // Update hologram to show active state
        updateDurationHologram();

        // Play sound effect for shooting
        world.playSound(armorStand.getLocation(), org.bukkit.Sound.ENTITY_ARROW_SHOOT, 0.5f, 1.5f);

        // Calculate shooting trajectory - shoot directly at target
        Location rayStart = armorStand.getLocation().clone().add(0, 1, 0); // Start from the head
        Location targetCenter = target.getLocation().add(0, target.getHeight() / 2, 0); // Aim at center of entity
        Vector direction = targetCenter.clone().subtract(rayStart).toVector().normalize();

        // Create a projectile animation
        new BukkitRunnable() {
            private final int maxSteps = 40; // Increased steps for more precision
            private int currentStep = 0;
            private final Location currentPos = rayStart.clone();
            private final Vector velocity = direction.clone().multiply(0.5); // Constant velocity in the direction of target
            private boolean hitTarget = false;
            private boolean hitBlock = false;

            @Override
            public void run() {
                // Check if we've completed the animation or hit something
                if (currentStep >= maxSteps || hitTarget || hitBlock) {
                    // Only apply damage if we actually hit the target
                    if (hitTarget) {
                        target.damage(damage, owner);

                        // Create impact particles
                        world.spawnParticle(Particle.CRIT, currentPos, 5, 0.2, 0.2, 0.2, 0.05);

                        // Play hit sound
                        world.playSound(currentPos, org.bukkit.Sound.ENTITY_ARROW_HIT, 0.5f, 1.0f);
                    }

                    this.cancel();
                    return;
                }

                // Move the current position using the velocity
                currentPos.add(velocity);

                // Display particles
                world.spawnParticle(shootParticle, currentPos, 1, 0, 0, 0, 0);

                // Check if we hit a block
                if (!currentPos.getBlock().isPassable()) {
                    hitBlock = true;
                    // Create block hit particles
                    world.spawnParticle(Particle.BLOCK_CRACK, currentPos, 10, 0.2, 0.2, 0.2, 0.05,
                            currentPos.getBlock().getBlockData());
                    return;
                }

                // Check if we're close to the target
                if (currentPos.distance(targetCenter) < 0.8) {
                    hitTarget = true;
                    return;
                }

                // Check if the target moved and we need to update our aim
                if (currentStep % 5 == 0 && !target.isDead()) {
                    // Get updated target position
                    Location newTargetPos = target.getLocation().add(0, target.getHeight() / 2, 0);

                    // Only update if target has moved significantly
                    if (newTargetPos.distance(targetCenter) > 1.0) {
                        // Update direction slightly to track moving targets
                        Vector newDirection = newTargetPos.clone().subtract(currentPos).toVector().normalize();
                        velocity.add(newDirection.multiply(0.1)).normalize().multiply(0.5);
                    }
                }

                currentStep++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // Run every tick for smooth animation
    }

    /**
     * Remove the turret entity and all associated tasks
     */
    public void remove() {
        // Cancel all tasks
        if (shootTask != null && !shootTask.isCancelled()) {
            shootTask.cancel();
        }
        if (removeTask != null && !removeTask.isCancelled()) {
            removeTask.cancel();
        }
        if (countdownTask != null && !countdownTask.isCancelled()) {
            countdownTask.cancel();
        }

        // Remove the armor stand
        if (armorStand != null && !armorStand.isDead()) {
            // Create particle effect for removal
            World world = armorStand.getWorld();
            if (world != null) {
                world.spawnParticle(Particle.CLOUD, armorStand.getLocation().clone().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.1);
            }

            armorStand.remove();
        }

        // Remove the hologram
        if (durationHologram != null) {
            try {
                HologramManager hologramManager = FancyHologramsPlugin.get().getHologramManager();
                // Use the stored hologram ID
                if (hologramId != null) {
                    hologramManager.removeHologram(durationHologram);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to remove duration hologram: " + e.getMessage());
            }
        }
    }
    /**
     * Get the location of the turret
     *
     * @return The location of the turret
     */
    public Location getLocation() {
        return location.clone();
    }

    /**
     * Get the UUID of the owner
     *
     * @return The UUID of the owner
     */
    public UUID getOwnerUuid() {
        return ownerUuid;
    }

    /**
     * Get the rarity of the turret
     *
     * @return The rarity of the turret
     */
    public String getRarity() {
        return rarity;
    }
}
