package me.zivush.grimoireenchant.mana;

import me.zivush.grimoireenchant.GrimoireEnchant;
import me.zivush.grimoireenchant.utils.ColorUtils;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.NamespacedKey;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.event.block.Action;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Manages mana cores for players
 */
public class ManaCoreManager implements Listener {

    private final GrimoireEnchant plugin;
    private final Map<UUID, String> activeCores = new HashMap<>();

    // NBT Keys for physical core items
    private final NamespacedKey coreIdKey;
    private final NamespacedKey physicalCoreKey;

    /**
     * Constructor for ManaCoreManager
     *
     * @param plugin The main plugin instance
     */
    public ManaCoreManager(GrimoireEnchant plugin) {
        this.plugin = plugin;

        // Initialize NBT keys
        this.coreIdKey = new NamespacedKey(plugin, "mana_core_id");
        this.physicalCoreKey = new NamespacedKey(plugin, "physical_mana_core");

        loadPlayerData();

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }



    /**
     * Load player data from the database
     */
    private void loadPlayerData() {
        if (plugin.getSpendingManager() == null) return;

        FileConfiguration database = plugin.getSpendingManager().getDatabase();

        for (String uuidStr : database.getKeys(false)) {
            try {
                UUID uuid = UUID.fromString(uuidStr);
                String activeCore = database.getString(uuidStr + ".active_core", "none");

                if (!activeCore.equals("none")) {
                    activeCores.put(uuid, activeCore);
                }
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid UUID in database: " + uuidStr);
            }
        }
    }

    /**
     * Save the database file
     */
    public void saveDatabase() {
        if (plugin.getSpendingManager() != null) {
            plugin.getSpendingManager().saveDatabase();
        }
    }

    /**
     * Get a player's active mana core
     *
     * @param player The player
     * @return The active core ID, or "none" if no core is active
     */
    public String getActiveCore(Player player) {
        return activeCores.getOrDefault(player.getUniqueId(), "none");
    }

    /**
     * Set a player's active mana core
     *
     * @param player The player
     * @param coreId The core ID to set as active, or "none" to remove
     */
    public void setActiveCore(Player player, String coreId) {
        UUID uuid = player.getUniqueId();

        if (coreId.equals("none")) {
            activeCores.remove(uuid);
        } else {
            activeCores.put(uuid, coreId);
        }

        if (plugin.getSpendingManager() != null) {
            FileConfiguration database = plugin.getSpendingManager().getDatabase();
            database.set(uuid.toString() + ".active_core", coreId);
            saveDatabase();
        }
    }



    /**
     * Get the maximum mana for a player based on their active core
     *
     * @param player The player
     * @return The maximum mana value
     */
    public int getMaxMana(Player player) {
        String coreId = getActiveCore(player);
        if (coreId.equals("none")) {
            return plugin.getConfig().getInt("Mana.Default-Mana", 100);
        }

        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + coreId);
        if (coreSection == null) {
            return plugin.getConfig().getInt("Mana.Default-Mana", 100);
        }

        return coreSection.getInt("Mana", plugin.getConfig().getInt("Mana.Default-Mana", 100));
    }

    /**
     * Get the mana regeneration rate for a player based on their active core
     *
     * @param player The player
     * @return The mana regeneration rate
     */
    public int getRegenRate(Player player) {
        String coreId = getActiveCore(player);
        if (coreId.equals("none")) {
            return plugin.getConfig().getInt("Mana.Default-Regen", 2);
        }

        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + coreId);
        if (coreSection == null) {
            return plugin.getConfig().getInt("Mana.Default-Regen", 2);
        }

        return coreSection.getInt("Regen", plugin.getConfig().getInt("Mana.Default-Regen", 2));
    }

    /**
     * Check if a player can use a specific rarity based on their active core
     *
     * @param player The player
     * @param rarity The rarity to check
     * @return True if the player can use the rarity
     */
    public boolean canUseRarity(Player player, String rarity) {
        String coreId = getActiveCore(player);

        // If no core is active, only allow common rarity
        if (coreId.equals("none")) {
            return rarity.equals("common");
        }

        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + coreId);
        if (coreSection == null) {
            return rarity.equals("common");
        }

        List<String> compatibleRarities = coreSection.getStringList("Compatible");
        return compatibleRarities.contains(rarity.toLowerCase());
    }

    /**
     * Create a mana core item
     *
     * @param coreId The core ID
     * @return The mana core item
     */
    public ItemStack createCoreItem(String coreId) {
        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + coreId);
        if (coreSection == null) return null;

        Material material = Material.valueOf(coreSection.getString("Item", "BEACON"));
        String name = coreSection.getString("Name", coreId);
        List<String> lore = coreSection.getStringList("Lore");
        int mana = coreSection.getInt("Mana", plugin.getConfig().getInt("Mana.Default-Mana", 100));
        int regen = coreSection.getInt("Regen", plugin.getConfig().getInt("Mana.Default-Regen", 2));

        // Process placeholders in lore
        List<String> processedLore = new ArrayList<>();
        for (String line : lore) {
            processedLore.add(line.replace("{MaxMana}", String.valueOf(mana))
                                  .replace("{RegenRate}", String.valueOf(regen)));
        }

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(ColorUtils.process(name));
            meta.setLore(processedLore.stream().map(ColorUtils::process).collect(Collectors.toList()));
            meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_ENCHANTS);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * Get all available mana core IDs
     *
     * @return List of all mana core IDs
     */
    public List<String> getAllCoreIds() {
        ConfigurationSection coresSection = plugin.getConfig().getConfigurationSection("ManaCores");
        if (coresSection == null) return new ArrayList<>();

        return new ArrayList<>(coresSection.getKeys(false));
    }

    /**
     * Create a physical mana core item that can be right-clicked to add to GUI
     *
     * @param coreId The core ID
     * @return The physical mana core item with NBT data
     */
    public ItemStack createPhysicalCoreItem(String coreId) {
        ItemStack coreItem = createCoreItem(coreId);
        if (coreItem == null) return null;

        // Add NBT data to mark this as a physical core
        ItemMeta meta = coreItem.getItemMeta();
        if (meta != null) {
            PersistentDataContainer container = meta.getPersistentDataContainer();
            container.set(physicalCoreKey, PersistentDataType.BYTE, (byte) 1);
            container.set(coreIdKey, PersistentDataType.STRING, coreId);

            // Add instruction to lore
            List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            lore.add("");
            lore.add(ColorUtils.process("&#A9A9A9ʀɪɢʜᴛ-ᴄʟɪᴄᴋ ᴛᴏ ᴇǫᴜɪᴘ ᴛʜɪs ᴄᴏʀᴇ"));
            lore.add(ColorUtils.process("&#A9A9A9sᴡᴀᴘs ᴡɪᴛʜ ᴄᴜʀʀᴇɴᴛ ᴇǫᴜɪᴘᴘᴇᴅ ᴄᴏʀᴇ"));
            meta.setLore(lore);

            coreItem.setItemMeta(meta);
        }

        return coreItem;
    }

    /**
     * Check if an item is a physical mana core
     *
     * @param item The item to check
     * @return True if the item is a physical mana core
     */
    public boolean isPhysicalCore(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) return false;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return false;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        return container.has(physicalCoreKey, PersistentDataType.BYTE);
    }

    /**
     * Get the core ID from a physical core item
     *
     * @param item The physical core item
     * @return The core ID, or null if not a physical core
     */
    public String getPhysicalCoreId(ItemStack item) {
        if (!isPhysicalCore(item)) return null;

        ItemMeta meta = item.getItemMeta();
        if (meta == null) return null;

        PersistentDataContainer container = meta.getPersistentDataContainer();
        return container.get(coreIdKey, PersistentDataType.STRING);
    }

    /**
     * Swap the player's active core with the core they're holding
     *
     * @param player The player
     * @param newCoreId The core ID to equip
     * @return True if the swap was successful, false if same core or inventory issues
     */
    public boolean swapCore(Player player, String newCoreId) {
        String currentCore = getActiveCore(player);

        // Check if trying to equip the same core
        if (currentCore.equals(newCoreId)) {
            return false; // Same core, no swap needed
        }

        ItemStack heldItem = player.getInventory().getItemInMainHand();

        // Remove the held core item
        if (heldItem.getAmount() > 1) {
            heldItem.setAmount(heldItem.getAmount() - 1);
        } else {
            player.getInventory().setItemInMainHand(null);
        }

        // If there's a current core, give it back to player
        if (!currentCore.equals("none")) {
            ItemStack oldCoreItem = createPhysicalCoreItem(currentCore);
            if (oldCoreItem != null) {
                // Try to add to inventory, drop if full
                if (player.getInventory().firstEmpty() == -1) {
                    player.getWorld().dropItemNaturally(player.getLocation(), oldCoreItem);
                    player.sendMessage(ColorUtils.process(
                            plugin.getMessagesConfig().getString("General.Prefix", "") +
                            plugin.getMessagesConfig().getString("ManaCores.InventoryFull", "&#FF6347ʏᴏᴜʀ ɪɴᴠᴇɴᴛᴏʀʏ ɪs ғᴜʟʟ! ᴛʜᴇ ᴄᴏʀᴇ ᴡᴀs ᴅʀᴏᴘᴘᴇᴅ ᴏɴ ᴛʜᴇ ɢʀᴏᴜɴᴅ.")));
                } else {
                    player.getInventory().addItem(oldCoreItem);
                }
            }
        }

        // Set the new core as active
        setActiveCore(player, newCoreId);
        return true;
    }

    /**
     * Unequip the current core and give it back to the player
     *
     * @param player The player
     * @return True if successful, false if inventory is full
     */
    public boolean unequipCore(Player player) {
        String currentCore = getActiveCore(player);

        if (currentCore.equals("none")) {
            return false; // No core to unequip
        }

        // Check if inventory has space
        if (player.getInventory().firstEmpty() == -1) {
            return false; // Inventory is full
        }

        // Create physical core item and give to player
        ItemStack coreItem = createPhysicalCoreItem(currentCore);
        if (coreItem != null) {
            player.getInventory().addItem(coreItem);
            setActiveCore(player, "none");
            return true;
        }

        return false;
    }

    /**
     * Handle right-click events for physical core items
     *
     * @param event The player interact event
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        if (!isPhysicalCore(item)) return;

        String coreId = getPhysicalCoreId(item);
        if (coreId == null) return;

        // Check if core exists in config
        ConfigurationSection coreSection = plugin.getConfig().getConfigurationSection("ManaCores." + coreId);
        if (coreSection == null) {
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    plugin.getMessagesConfig().getString("ManaCores.InvalidCore", "&#FF6347ᴍᴀɴᴀ ᴄᴏʀᴇ &#FFCC33{core} &#FF6347ɴᴏᴛ ғᴏᴜɴᴅ.")
                            .replace("{core}", coreId)));
            return;
        }

        // Check if the core GUI is open - if so, swap cores
        if (plugin.getManaCoreGUI().isGuiOpen(player)) {
            if (swapCore(player, coreId)) {
                // Send success message
                player.sendMessage(ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                        plugin.getMessagesConfig().getString("ManaCores.Equipped", "&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ ᴇǫᴜɪᴘᴘᴇᴅ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ.")
                                .replace("{core}", coreId)));

                // Reopen the GUI to show the change
                plugin.getManaCoreGUI().openGui(player);
            } else {
                // Same core message
                player.sendMessage(ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                        "&#FF6347ʏᴏᴜ ᴀʟʀᴇᴀᴅʏ ʜᴀᴠᴇ ᴛʜɪs ᴄᴏʀᴇ ᴇǫᴜɪᴘᴘᴇᴅ!"));
            }
            event.setCancelled(true);
            return;
        }

        // If GUI is not open, just equip the core directly
        if (swapCore(player, coreId)) {
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    plugin.getMessagesConfig().getString("ManaCores.Equipped", "&#4CBB17ʏᴏᴜ ʜᴀᴠᴇ ᴇǫᴜɪᴘᴘᴇᴅ ᴛʜᴇ &#FFFFFF{core} &#4CBB17ᴍᴀɴᴀ ᴄᴏʀᴇ.")
                            .replace("{core}", coreId)));
        } else {
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    "&#FF6347ʏᴏᴜ ᴀʟʀᴇᴀᴅʏ ʜᴀᴠᴇ ᴛʜɪs ᴄᴏʀᴇ ᴇǫᴜɪᴘᴘᴇᴅ!"));
        }

        event.setCancelled(true);
    }

    /**
     * Handle block place events to prevent placing physical cores
     *
     * @param event The block place event
     */
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        ItemStack item = event.getItemInHand();

        // Check if the item being placed is a physical core
        if (isPhysicalCore(item)) {
            event.setCancelled(true);

            // Send message to player
            Player player = event.getPlayer();
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    plugin.getMessagesConfig().getString("ManaCores.CannotPlace", "&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴘʟᴀᴄᴇ ᴍᴀɴᴀ ᴄᴏʀᴇs ᴀs ʙʟᴏᴄᴋs!")));
        }
    }

    /**
     * Handle player quit event to save their data
     *
     * @param event The player quit event
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();

        // Save player data
        String activeCore = activeCores.getOrDefault(uuid, "none");

        if (plugin.getSpendingManager() != null) {
            FileConfiguration database = plugin.getSpendingManager().getDatabase();
            database.set(uuid.toString() + ".active_core", activeCore);
            saveDatabase();
        }
    }
}
