package me.zivush.grimoireenchant;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.scheduler.BukkitScheduler;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import me.zivush.grimoireenchant.utils.ColorUtils;

import java.util.*;

/**
 * Manages all GUI interactions for the plugin
 */
public class GuiManager implements Listener {

    private final GrimoireEnchant plugin;
    private final Map<UUID, ItemStack> pendingEnchantments = new HashMap<>();
    private final Map<UUID, ItemStack> targetItems = new HashMap<>();
    private final Map<UUID, Integer> targetSlots = new HashMap<>();
    private final Map<UUID, Integer> targetSlotTypes = new HashMap<>();
    private final Map<UUID, Inventory> targetInventories = new HashMap<>();
    private final Map<UUID, ItemStack> originalCursorItems = new HashMap<>();
    private final Set<UUID> bookAlreadyReturned = new HashSet<>();

    // Maps for combine functionality
    private final Map<UUID, ItemStack> pendingCombineBook1 = new HashMap<>();
    private final Map<UUID, ItemStack> pendingCombineBook2 = new HashMap<>();
    private final Map<UUID, Integer> combineBook1Slot = new HashMap<>();
    private final Map<UUID, Integer> combineBook2Slot = new HashMap<>();
    private final Map<UUID, Inventory> combineInventories = new HashMap<>();
    private final Set<UUID> combineItemsReturned = new HashSet<>();

    /**
     * Constructor for GuiManager
     *
     * @param plugin The main plugin instance
     */
    public GuiManager(GrimoireEnchant plugin) {
        this.plugin = plugin;

        // Register events
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // Ensure GUI entries exist for all Summons-based enchantments
        ensureSummonsGuiEntries();
    }

    /**
     * Ensure that GUI entries exist for all Summons-based enchantments
     * This method checks the config for Summons variants and creates GUI entries if needed
     */
    private void ensureSummonsGuiEntries() {
        // Get all abilities from config
        ConfigurationSection abilitiesSection = plugin.getConfig().getConfigurationSection("Abilities");
        if (abilitiesSection == null) return;

        // Look for Summons-based enchantments
        for (String key : abilitiesSection.getKeys(false)) {
            // Check if this is a Summons variant but not the base Summons
            if (key.startsWith("Summons") && !key.equals("Summons")) {
                // Check if a GUI entry already exists
                if (plugin.getGuiConfig().getConfigurationSection("EnchantmentBooks." + key) == null) {
                    // No GUI entry exists, create one based on the Summons template
                    plugin.getLogger().info("Creating GUI entry for " + key + " based on Summons template");
                }
            }
        }
    }

    /**
     * Open the confirmation GUI for a player
     *
     * @param player The player
     * @param enchantBook The enchantment book
     * @param targetItem The item to enchant
     * @param slot The inventory slot of the target item
     * @param slotType The type of slot
     * @param clickedInventory The inventory that was clicked
     */
    public void openConfirmationGUI(Player player, ItemStack enchantBook, ItemStack targetItem, int slot, int slotType, Inventory clickedInventory) {
        // Check if the player is in a non-player inventory (like chest, anvil, etc.)
        if (player.getOpenInventory().getType() != InventoryType.CRAFTING &&
            player.getOpenInventory().getType() != InventoryType.CREATIVE) {
            // Player is in another inventory, don't open the GUI
            String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.InventoryOpen",
                    "&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴀᴘᴘʟʏ ᴀ ɢʀɪᴍᴏɪʀᴇ ᴡʜɪʟᴇ ɪɴ ᴀɴᴏᴛʜᴇʀ ɪɴᴠᴇɴᴛᴏʀʏ!");
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));

            // Return the book to the player's cursor
            player.setItemOnCursor(enchantBook.clone());
            return;
        }

        // Store the items and slot information
        UUID playerUuid = player.getUniqueId();
        pendingEnchantments.put(playerUuid, enchantBook);
        targetItems.put(playerUuid, targetItem);
        targetSlots.put(playerUuid, slot);
        targetSlotTypes.put(playerUuid, slotType);
        targetInventories.put(playerUuid, clickedInventory);

        // Store the original cursor item (the book)
        originalCursorItems.put(playerUuid, enchantBook.clone());

        // Debug message
        if (plugin.getConfig().getBoolean("Debug", false)) {
            player.sendMessage(ColorUtils.process("&7[Debug] Stored original book for possible return."));
        }

        // Check if the player has enough currency if economy is enabled
        String rarity = plugin.getEnchantmentManager().getEnchantmentRarity(enchantBook);
        if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled()) {
            double cost = plugin.getEconomyManager().getCost(rarity);
            if (!plugin.getEconomyManager().hasEnough(player, cost)) {
                player.sendMessage(ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                        plugin.getEconomyManager().getNotEnoughCurrencyMessage(player, cost)));
                return;
            }
        }
        ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("ConfirmationGUI");
        if (config == null) return;

        String title = ColorUtils.process(config.getString("Title", "Apply Enchantment?"));
        int size = config.getInt("Size", 27);

        Inventory inventory = Bukkit.createInventory(null, size, title);

        // Add border items
        ConfigurationSection borderConfig = config.getConfigurationSection("BorderItem");
        if (borderConfig != null) {
            Material borderMaterial = Material.valueOf(borderConfig.getString("Material", "GRAY_STAINED_GLASS_PANE"));
            String borderName = ColorUtils.process(borderConfig.getString("Name", " "));

            ItemStack borderItem = new ItemStack(borderMaterial);
            ItemMeta borderMeta = borderItem.getItemMeta();
            if (borderMeta != null) {
                borderMeta.setDisplayName(borderName);
                borderItem.setItemMeta(borderMeta);
            }

            for (int slotIndex : borderConfig.getIntegerList("Slots")) {
                if (slotIndex < size) {
                    inventory.setItem(slotIndex, borderItem);
                }
            }
        }

        // Add confirm button
        ConfigurationSection confirmConfig = config.getConfigurationSection("ConfirmItem");
        if (confirmConfig != null) {
            Material confirmMaterial = Material.valueOf(confirmConfig.getString("Material", "LIME_STAINED_GLASS_PANE"));
            String confirmName = ColorUtils.process(confirmConfig.getString("Name", "&a&lConfirm"));

            ItemStack confirmItem = new ItemStack(confirmMaterial);
            ItemMeta confirmMeta = confirmItem.getItemMeta();
            if (confirmMeta != null) {
                confirmMeta.setDisplayName(confirmName);

                List<String> confirmLore = new ArrayList<>();
                rarity = plugin.getEnchantmentManager().getEnchantmentRarity(enchantBook);
                double cost = 0.0;
                String currencyName = "";

                if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled()) {
                    cost = plugin.getEconomyManager().getCost(rarity);
                    currencyName = plugin.getEconomyManager().getCurrencyName();
                }

                for (String line : confirmConfig.getStringList("Lore")) {
                    // Replace cost and currency placeholders
                    line = line.replace("{cost}", plugin.getEconomyManager().formatCurrency(cost))
                               .replace("{currency}", currencyName);
                    confirmLore.add(ColorUtils.process(line));
                }
                confirmMeta.setLore(confirmLore);

                confirmItem.setItemMeta(confirmMeta);
            }

            int confirmSlot = confirmConfig.getInt("Slot", 11);
            if (confirmSlot < size) {
                inventory.setItem(confirmSlot, confirmItem);
            }
        }

        // Add cancel button
        ConfigurationSection cancelConfig = config.getConfigurationSection("CancelItem");
        if (cancelConfig != null) {
            Material cancelMaterial = Material.valueOf(cancelConfig.getString("Material", "RED_STAINED_GLASS_PANE"));
            String cancelName = ColorUtils.process(cancelConfig.getString("Name", "&c&lCancel"));

            ItemStack cancelItem = new ItemStack(cancelMaterial);
            ItemMeta cancelMeta = cancelItem.getItemMeta();
            if (cancelMeta != null) {
                cancelMeta.setDisplayName(cancelName);

                List<String> cancelLore = new ArrayList<>();
                for (String line : cancelConfig.getStringList("Lore")) {
                    cancelLore.add(ColorUtils.process(line));
                }
                cancelMeta.setLore(cancelLore);

                cancelItem.setItemMeta(cancelMeta);
            }

            int cancelSlot = cancelConfig.getInt("Slot", 15);
            if (cancelSlot < size) {
                inventory.setItem(cancelSlot, cancelItem);
            }
        }

        // Add info item
        ConfigurationSection infoConfig = config.getConfigurationSection("InfoItem");
        if (infoConfig != null) {
            Material infoMaterial = Material.valueOf(infoConfig.getString("Material", "BOOK"));
            String infoName = ColorUtils.process(infoConfig.getString("Name", "&e&lEnchantment Info"));

            ItemStack infoItem = new ItemStack(infoMaterial);
            ItemMeta infoMeta = infoItem.getItemMeta();
            if (infoMeta != null) {
                infoMeta.setDisplayName(infoName);

                // Get enchantment info
                String enchantId = plugin.getEnchantmentManager().getEnchantmentId(enchantBook);
                rarity = plugin.getEnchantmentManager().getEnchantmentRarity(enchantBook);

                if (enchantId != null && rarity != null) {
                    GrimoireEnchantment enchantment = plugin.getEnchantmentManager().getEnchantment(enchantId);
                    if (enchantment != null) {
                        List<String> infoLore = new ArrayList<>();
                        for (String line : infoConfig.getStringList("Lore")) {
                            // Special handling for description placeholder
                            if (line.contains("{description}")) {
                                // Get the formatting/color from the line
                                String prefix = "";
                                int descIndex = line.indexOf("{description}");
                                if (descIndex > 0) {
                                    prefix = line.substring(0, descIndex);
                                }

                                // Add each description line with the same prefix
                                for (String descLine : enchantment.getDescriptionLines()) {
                                    String formattedLine = line.replace("{description}", descLine);
                                    infoLore.add(ColorUtils.process(formattedLine));
                                }
                            } else {
                                // Normal placeholder replacement
                                line = line.replace("{enchant_name}", enchantment.getDisplay())
                                          .replace("{rarity}", plugin.getConfig().getString("Rarity." + rarity, rarity));
                                infoLore.add(ColorUtils.process(line));
                            }
                        }
                        infoMeta.setLore(infoLore);
                    }
                }

                infoItem.setItemMeta(infoMeta);
            }

            int infoSlot = infoConfig.getInt("Slot", 13);
            if (infoSlot < size) {
                inventory.setItem(infoSlot, infoItem);
            }
        }

        // Items and slot information already stored

        // Open the inventory
        player.openInventory(inventory);
    }

    /**
     * Open the combine confirmation GUI for a player
     *
     * @param player The player
     * @param book1 The first grimoire book
     * @param book2 The second grimoire book
     * @param book1Slot The inventory slot of the first book
     * @param book2Slot The inventory slot of the second book
     * @param clickedInventory The inventory that was clicked
     */
    public void openCombineGUI(Player player, ItemStack book1, ItemStack book2, int book1Slot, int book2Slot, Inventory clickedInventory) {
        // Check if the player is in a non-player inventory (like chest, anvil, etc.)
        if (player.getOpenInventory().getType() != InventoryType.CRAFTING &&
            player.getOpenInventory().getType() != InventoryType.CREATIVE) {
            // Player is in another inventory, don't open the GUI
            String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.InventoryOpen",
                    "&#FF6347ʏᴏᴜ ᴄᴀɴɴᴏᴛ ᴄᴏᴍʙɪɴᴇ ɢʀɪᴍᴏɪʀᴇs ᴡʜɪʟᴇ ɪɴ ᴀɴᴏᴛʜᴇʀ ɪɴᴠᴇɴᴛᴏʀʏ!");
            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));

            // Return the book to the player's cursor
            player.setItemOnCursor(book1.clone());
            return;
        }

        // Store the items and slot information
        UUID playerUuid = player.getUniqueId();
        pendingCombineBook1.put(playerUuid, book1.clone());
        pendingCombineBook2.put(playerUuid, book2.clone());
        combineBook1Slot.put(playerUuid, book1Slot);
        combineBook2Slot.put(playerUuid, book2Slot);
        combineInventories.put(playerUuid, clickedInventory);

        // Store the original cursor item (for returning on cancel)
        originalCursorItems.put(playerUuid, player.getItemOnCursor().clone());

        // Debug message
        if (plugin.getConfig().getBoolean("Debug", false)) {
            player.sendMessage(ColorUtils.process("&7[Debug] Stored original books for possible return."));
        }

        // Get enchantment info
        String enchantId = plugin.getEnchantmentManager().getEnchantmentId(book1);
        String rarity = plugin.getEnchantmentManager().getEnchantmentRarity(book1);
        String nextRarity = plugin.getEnchantmentManager().getNextRarity(rarity);

        if (enchantId == null || rarity == null || nextRarity == null) {
            player.sendMessage(ColorUtils.process(
                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                    plugin.getMessagesConfig().getString("Enchantment.MaxRarity", "&#FF6347ᴛʜɪs ɢʀɪᴍᴏɪʀᴇ ɪs ᴀʟʀᴇᴀᴅʏ ᴀᴛ ᴛʜᴇ ʜɪɢʜᴇsᴛ ʀᴀʀɪᴛʏ ᴀɴᴅ ᴄᴀɴɴᴏᴛ ʙᴇ ᴜᴘɢʀᴀᴅᴇᴅ ғᴜʀᴛʜᴇʀ.")));
            return;
        }

        // Check if the player has enough currency if economy is enabled
        if (plugin.getEconomyManager().isCoinsEngineEnabled()) {
            double cost = plugin.getEconomyManager().getCombineCost(rarity);
            if (!plugin.getEconomyManager().hasEnough(player, cost)) {
                player.sendMessage(ColorUtils.process(
                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                        plugin.getEconomyManager().getNotEnoughCurrencyMessage(player, cost)));
                return;
            }
        }

        ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("CombineGUI");
        if (config == null) return;

        String title = ColorUtils.process(config.getString("Title", "Combine Grimoires?"));
        int size = config.getInt("Size", 27);

        Inventory inventory = Bukkit.createInventory(null, size, title);

        // Add border items
        ConfigurationSection borderConfig = config.getConfigurationSection("BorderItem");
        if (borderConfig != null) {
            Material borderMaterial = Material.valueOf(borderConfig.getString("Material", "GRAY_STAINED_GLASS_PANE"));
            String borderName = ColorUtils.process(borderConfig.getString("Name", " "));

            ItemStack borderItem = new ItemStack(borderMaterial);
            ItemMeta borderMeta = borderItem.getItemMeta();
            if (borderMeta != null) {
                borderMeta.setDisplayName(borderName);
                borderItem.setItemMeta(borderMeta);
            }

            for (int slotIndex : borderConfig.getIntegerList("Slots")) {
                if (slotIndex < size) {
                    inventory.setItem(slotIndex, borderItem);
                }
            }
        }

        // Add confirm button
        ConfigurationSection confirmConfig = config.getConfigurationSection("ConfirmItem");
        if (confirmConfig != null) {
            Material confirmMaterial = Material.valueOf(confirmConfig.getString("Material", "LIME_STAINED_GLASS_PANE"));
            String confirmName = ColorUtils.process(confirmConfig.getString("Name", "&a&lCombine"));

            ItemStack confirmItem = new ItemStack(confirmMaterial);
            ItemMeta confirmMeta = confirmItem.getItemMeta();
            if (confirmMeta != null) {
                confirmMeta.setDisplayName(confirmName);

                List<String> confirmLore = new ArrayList<>();

                // Get currency info if economy is enabled
                String currencyName = "coins";
                double cost = 0.0;

                if (plugin.getEconomyManager().isCoinsEngineEnabled()) {
                    currencyName = plugin.getEconomyManager().getCurrencyName();
                    cost = plugin.getEconomyManager().getCombineCost(rarity);
                }

                for (String line : confirmConfig.getStringList("Lore")) {
                    // Replace cost and currency placeholders
                    line = line.replace("{cost}", plugin.getEconomyManager().formatCurrency(cost))
                               .replace("{currency}", currencyName);
                    confirmLore.add(ColorUtils.process(line));
                }
                confirmMeta.setLore(confirmLore);

                confirmItem.setItemMeta(confirmMeta);
            }

            int confirmSlot = confirmConfig.getInt("Slot", 11);
            if (confirmSlot < size) {
                inventory.setItem(confirmSlot, confirmItem);
            }
        }

        // Add cancel button
        ConfigurationSection cancelConfig = config.getConfigurationSection("CancelItem");
        if (cancelConfig != null) {
            Material cancelMaterial = Material.valueOf(cancelConfig.getString("Material", "RED_STAINED_GLASS_PANE"));
            String cancelName = ColorUtils.process(cancelConfig.getString("Name", "&c&lCancel"));

            ItemStack cancelItem = new ItemStack(cancelMaterial);
            ItemMeta cancelMeta = cancelItem.getItemMeta();
            if (cancelMeta != null) {
                cancelMeta.setDisplayName(cancelName);

                List<String> cancelLore = new ArrayList<>();
                for (String line : cancelConfig.getStringList("Lore")) {
                    cancelLore.add(ColorUtils.process(line));
                }
                cancelMeta.setLore(cancelLore);

                cancelItem.setItemMeta(cancelMeta);
            }

            int cancelSlot = cancelConfig.getInt("Slot", 15);
            if (cancelSlot < size) {
                inventory.setItem(cancelSlot, cancelItem);
            }
        }

        // Add info item
        ConfigurationSection infoConfig = config.getConfigurationSection("InfoItem");
        if (infoConfig != null) {
            Material infoMaterial = Material.valueOf(infoConfig.getString("Material", "BOOK"));
            String infoName = ColorUtils.process(infoConfig.getString("Name", "&e&lCombine Info"));

            ItemStack infoItem = new ItemStack(infoMaterial);
            ItemMeta infoMeta = infoItem.getItemMeta();
            if (infoMeta != null) {
                infoMeta.setDisplayName(infoName);

                // Get enchantment info
                GrimoireEnchantment enchantment = plugin.getEnchantmentManager().getEnchantment(enchantId);

                if (enchantment != null) {
                    List<String> infoLore = new ArrayList<>();
                    for (String line : infoConfig.getStringList("Lore")) {
                        // Special handling for description placeholder
                        if (line.contains("{description}")) {
                            // Get the formatting/color from the line
                            String prefix = "";
                            int descIndex = line.indexOf("{description}");
                            if (descIndex > 0) {
                                prefix = line.substring(0, descIndex);
                            }

                            // Add each description line with the same prefix
                            for (String descLine : enchantment.getDescriptionLines()) {
                                String formattedLine = line.replace("{description}", descLine);
                                infoLore.add(ColorUtils.process(formattedLine));
                            }
                        } else {
                            // Normal placeholder replacement
                            line = line.replace("{enchant_name}", enchantment.getDisplay())
                                      .replace("{rarity}", plugin.getConfig().getString("Rarity." + rarity, rarity))
                                      .replace("{new_rarity}", plugin.getConfig().getString("Rarity." + nextRarity, nextRarity));
                            infoLore.add(ColorUtils.process(line));
                        }
                    }
                    infoMeta.setLore(infoLore);
                }

                infoItem.setItemMeta(infoMeta);
            }

            int infoSlot = infoConfig.getInt("Slot", 13);
            if (infoSlot < size) {
                inventory.setItem(infoSlot, infoItem);
            }
        }

        // Open the inventory
        player.openInventory(inventory);
    }

    /**
     * Handle inventory click events
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;

        Player player = (Player) event.getWhoClicked();
        UUID uuid = player.getUniqueId();

        // Check for combine GUI
        if (pendingCombineBook1.containsKey(uuid) && pendingCombineBook2.containsKey(uuid)) {
            ConfigurationSection combineConfig = plugin.getGuiConfig().getConfigurationSection("CombineGUI");
            if (combineConfig != null) {
                String combineTitle = ColorUtils.process(combineConfig.getString("Title", "Combine Grimoires?"));

                if (event.getView().getTitle().equals(combineTitle)) {
                    event.setCancelled(true);

                    int confirmSlot = combineConfig.getConfigurationSection("ConfirmItem").getInt("Slot", 11);
                    int cancelSlot = combineConfig.getConfigurationSection("CancelItem").getInt("Slot", 15);

                    if (event.getRawSlot() == confirmSlot) {
                        // Confirm button clicked - combine the grimoires
                        combineItemsReturned.add(uuid);

                        if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Combine confirm button clicked."));
                        }

                        ItemStack book1 = pendingCombineBook1.get(uuid);
                        ItemStack book2 = pendingCombineBook2.get(uuid);

                        // Get enchantment info
                        String enchantId = plugin.getEnchantmentManager().getEnchantmentId(book1);
                        String rarity = plugin.getEnchantmentManager().getEnchantmentRarity(book1);

                        // Check if economy is enabled and player has enough currency
                        double cost = 0.0;
                        if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled()) {
                            cost = plugin.getEconomyManager().getCombineCost(rarity);
                            if (!plugin.getEconomyManager().hasEnough(player, cost)) {
                                player.sendMessage(ColorUtils.process(
                                        plugin.getMessagesConfig().getString("General.Prefix", "") +
                                        plugin.getEconomyManager().getNotEnoughCurrencyMessage(player, cost)));
                                player.closeInventory();
                                return;
                            }
                        }

                        // Create the upgraded grimoire
                        ItemStack upgradedBook = plugin.getEnchantmentManager().upgradeGrimoireRarity(book1);

                        if (upgradedBook != null) {
                            // Get the inventory and slot information
                            Inventory inventory = combineInventories.get(uuid);
                            int slot1 = combineBook1Slot.get(uuid);
                            int slot2 = combineBook2Slot.get(uuid);

                            // Schedule a task to handle the book removal and upgrade after the GUI is closed
                            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                                // Clear cursor first
                                player.setItemOnCursor(null);

                                // Remove the original books
                                if (inventory != null) {
                                    // Remove the first book
                                    if (slot1 >= 0) {
                                        ItemStack currentItem = inventory.getItem(slot1);
                                        if (currentItem != null && currentItem.getAmount() > 1) {
                                            currentItem.setAmount(currentItem.getAmount() - 1);
                                            inventory.setItem(slot1, currentItem);
                                        } else {
                                            inventory.setItem(slot1, null);
                                        }
                                    }

                                    // Remove the second book (if it's in inventory)
                                    if (slot2 >= 0) {
                                        ItemStack currentItem = inventory.getItem(slot2);
                                        if (currentItem != null && currentItem.getAmount() > 1) {
                                            currentItem.setAmount(currentItem.getAmount() - 1);
                                            inventory.setItem(slot2, currentItem);
                                        } else {
                                            inventory.setItem(slot2, null);
                                        }
                                    } else {
                                        // If slot2 is -1, it means the book was in cursor
                                        // We've already cleared the cursor above
                                    }

                                    // Force inventory update
                                    if (inventory.getType() == InventoryType.CRAFTING) {
                                        player.updateInventory();
                                    }
                                }

                                // Give the player the upgraded book
                                HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(upgradedBook);
                                if (!leftover.isEmpty()) {
                                    // If inventory is full, drop the book at player's location
                                    for (ItemStack item : leftover.values()) {
                                        player.getWorld().dropItemNaturally(player.getLocation(), item);
                                    }
                                }

                                player.updateInventory();
                            }, 2L); // Slight delay to ensure inventory is updated properly

                            // Take currency if economy is enabled
                            if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled() && cost > 0) {
                                plugin.getEconomyManager().takeCurrency(player, cost);

                                // Track spending
                                if (plugin.getSpendingManager() != null) {
                                    plugin.getSpendingManager().addSpending(player, cost);
                                }

                                // Send currency success message
                                String currencyMsg = plugin.getMessagesConfig().getString("Economy.Success",
                                        "&#4CBB17ʏᴏᴜ ᴘᴀɪᴅ &#FFFFFF{cost} &#4CBB17{currency} ғᴏʀ ᴛʜᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ.");
                                currencyMsg = currencyMsg.replace("{cost}", plugin.getEconomyManager().formatCurrency(cost))
                                                        .replace("{currency}", plugin.getEconomyManager().getCurrencyName());
                                player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + currencyMsg));
                            }

                            // Send success message
                            String nextRarity = plugin.getEnchantmentManager().getNextRarity(rarity);
                            String rarityDisplay = plugin.getConfig().getString("Rarity." + nextRarity, nextRarity);
                            String successMsg = plugin.getMessagesConfig().getString("Enchantment.CombineSuccess",
                                    "&#4CBB17ɢʀɪᴍᴏɪʀᴇs ᴄᴏᴍʙɪɴᴇᴅ sᴜᴄᴄᴇssғᴜʟʟʏ! ʏᴏᴜ ʀᴇᴄᴇɪᴠᴇᴅ ᴀ {new_rarity} ɢʀɪᴍᴏɪʀᴇ!");
                            successMsg = successMsg.replace("{new_rarity}", rarityDisplay);
                            player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + successMsg));

                            // Play particles if enabled
                            if (plugin.getConfig().getBoolean("Economy.Apply-Particles.Enabled", true)) {
                                String particleType = plugin.getConfig().getString("Economy.Apply-Particles." + nextRarity + ".Type", "VILLAGER_HAPPY");
                                int particleCount = plugin.getConfig().getInt("Economy.Apply-Particles." + nextRarity + ".Count", 15);

                                try {
                                    Particle particle = Particle.valueOf(particleType);
                                    player.getWorld().spawnParticle(particle, player.getLocation().add(0, 1, 0), particleCount, 0.5, 0.5, 0.5, 0.1);
                                } catch (IllegalArgumentException e) {
                                    // Invalid particle type, use default
                                    player.getWorld().spawnParticle(Particle.VILLAGER_HAPPY, player.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
                                }
                            }

                            // Play sound if enabled
                            if (plugin.getConfig().getBoolean("Economy.Apply-Sounds.Enabled", true)) {
                                String soundName = plugin.getConfig().getString("Economy.Apply-Sounds." + nextRarity + ".Sound", "ENTITY_PLAYER_LEVELUP");
                                float volume = (float) plugin.getConfig().getDouble("Economy.Apply-Sounds." + nextRarity + ".Volume", 0.5);
                                float pitch = (float) plugin.getConfig().getDouble("Economy.Apply-Sounds." + nextRarity + ".Pitch", 1.0);

                                try {
                                    Sound sound = Sound.valueOf(soundName);
                                    player.playSound(player.getLocation(), sound, volume, pitch);
                                } catch (IllegalArgumentException e) {
                                    // Invalid sound, use default
                                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.0f);
                                }
                            }
                        }

                        // Close the inventory
                        player.closeInventory();
                    } else if (event.getRawSlot() == cancelSlot) {
                        // Cancel button clicked
                        combineItemsReturned.add(uuid);

                        if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Combine cancel button clicked."));
                        }

                        // Get the original cursor item
                        ItemStack bookToReturn = originalCursorItems.get(uuid);

                        // Make sure we have a final reference for the task
                        final ItemStack finalBookToReturn = bookToReturn != null ? bookToReturn.clone() : null;

                        // Close the inventory first
                        player.closeInventory();

                        // Schedule a task to handle the book return after inventory is closed
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            // Clear cursor first
                            player.setItemOnCursor(null);

                            // Debug message
                            if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] Returning book to inventory..."));
                            }

                            // Return the book to the player's inventory
                            if (finalBookToReturn != null) {
                                // Try to add the book to the player's inventory
                                HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(finalBookToReturn);

                                if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Attempting to return book: " +
                                        finalBookToReturn.getType() + " x" + finalBookToReturn.getAmount()));
                                }

                                if (!leftover.isEmpty()) {
                                    // If inventory is full, drop the book at player's location
                                    for (ItemStack item : leftover.values()) {
                                        player.getWorld().dropItemNaturally(player.getLocation(), item);

                                        if (plugin.getConfig().getBoolean("Debug", false)) {
                                            player.sendMessage(ColorUtils.process("&7[Debug] Inventory full, dropped book."));
                                        }
                                    }
                                } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Book returned to inventory successfully."));
                                }
                            } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] No book to return!"));
                            }

                            player.updateInventory();
                        }, 2L);

                        // Send cancel message
                        String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.CombineCancel", "&#FF6347ɢʀɪᴍᴏɪʀᴇ ᴄᴏᴍʙɪɴᴀᴛɪᴏɴ ᴄᴀɴᴄᴇʟʟᴇᴅ.");
                        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));
                    }

                    return;
                }
            }
        }

        // Check if this is our confirmation GUI
        if (!pendingEnchantments.containsKey(uuid)) return;

        ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("ConfirmationGUI");
        if (config == null) return;

        String title = ColorUtils.process(config.getString("Title", "Apply Enchantment?"));

        if (event.getView().getTitle().equals(title)) {
            event.setCancelled(true);

            int confirmSlot = config.getConfigurationSection("ConfirmItem").getInt("Slot", 11);
            int cancelSlot = config.getConfigurationSection("CancelItem").getInt("Slot", 15);

            if (event.getRawSlot() == confirmSlot) {
                // Confirm button clicked
                // Mark that we don't need to return the book on close
                bookAlreadyReturned.add(uuid);

                if (plugin.getConfig().getBoolean("Debug", false)) {
                    player.sendMessage(ColorUtils.process("&7[Debug] Confirm button clicked, marked book as used."));
                }

                ItemStack enchantBook = pendingEnchantments.get(uuid);
                ItemStack targetItem = targetItems.get(uuid);

                if (targetItem != null) {
                    // Check if economy is enabled and player has enough currency
                    String rarity = plugin.getEnchantmentManager().getEnchantmentRarity(enchantBook);
                    double cost = 0.0;

                    if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled()) {
                        cost = plugin.getEconomyManager().getCost(rarity);
                        if (!plugin.getEconomyManager().hasEnough(player, cost)) {
                            player.sendMessage(ColorUtils.process(
                                    plugin.getMessagesConfig().getString("General.Prefix", "") +
                                    plugin.getEconomyManager().getNotEnoughCurrencyMessage(player, cost)));
                            player.closeInventory();
                            return;
                        }
                    }

                    // Apply the enchantment
                    ItemStack enchantedItem = plugin.getEnchantmentManager().applyEnchantment(targetItem, enchantBook);

                    // Get the inventory and slot information
                    Inventory inventory = targetInventories.get(uuid);
                    int slot = targetSlots.get(uuid);

                    // Update the item in the inventory
                    if (inventory != null) {
                        inventory.setItem(slot, enchantedItem);

                        // Force inventory update to ensure changes are visible
                        if (inventory.getType() == InventoryType.CRAFTING) {
                            // Player inventory
                            player.updateInventory();
                        }
                    }

                    // Find and remove one enchantment book from the player's inventory
                    // Schedule this to happen after the GUI is closed to avoid inventory issues
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        // First check if the book is in the cursor
                        ItemStack cursorItem = player.getItemOnCursor();
                        String enchantId = plugin.getEnchantmentManager().getEnchantmentId(enchantBook);

                        if (cursorItem != null && cursorItem.getType() == Material.ENCHANTED_BOOK &&
                            plugin.getEnchantmentManager().hasEnchantment(cursorItem) &&
                            plugin.getEnchantmentManager().getEnchantmentId(cursorItem).equals(enchantId)) {

                            // Book is in cursor, reduce amount by 1
                            if (cursorItem.getAmount() > 1) {
                                cursorItem.setAmount(cursorItem.getAmount() - 1);
                                player.setItemOnCursor(cursorItem);
                            } else {
                                player.setItemOnCursor(null);
                            }
                        } else {
                            // Book is not in cursor, search inventory
                            ItemStack[] contents = player.getInventory().getContents();
                            for (int i = 0; i < contents.length; i++) {
                                ItemStack item = contents[i];
                                if (item != null && item.getType() == Material.ENCHANTED_BOOK &&
                                    plugin.getEnchantmentManager().hasEnchantment(item) &&
                                    plugin.getEnchantmentManager().getEnchantmentId(item).equals(enchantId)) {

                                    // Found the book in inventory, reduce amount by 1
                                    if (item.getAmount() > 1) {
                                        item.setAmount(item.getAmount() - 1);
                                        player.getInventory().setItem(i, item);
                                    } else {
                                        player.getInventory().setItem(i, null);
                                    }
                                    player.updateInventory();
                                    break;
                                }
                            }
                        }
                    }, 2L); // Slight delay to ensure inventory is updated properly

                    // Take currency if economy is enabled
                    if (rarity != null && plugin.getEconomyManager().isCoinsEngineEnabled() && cost > 0) {
                        plugin.getEconomyManager().takeCurrency(player, cost);

                        // Track spending
                        if (plugin.getSpendingManager() != null) {
                            plugin.getSpendingManager().addSpending(player, cost);
                        }

                        // Send currency success message
                        String currencyMsg = plugin.getMessagesConfig().getString("Economy.Success",
                                "&#4CBB17ʏᴏᴜ ᴘᴀɪᴅ &#FFFFFF{cost} &#4CBB17{currency} ғᴏʀ ᴛʜᴇ ᴇɴᴄʜᴀɴᴛᴍᴇɴᴛ.");
                        currencyMsg = currencyMsg.replace("{cost}", plugin.getEconomyManager().formatCurrency(cost))
                                                .replace("{currency}", plugin.getEconomyManager().getCurrencyName());
                        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + currencyMsg));

                        // Play particles if enabled
                        if (plugin.getConfig().getBoolean("Economy.Apply-Particles.Enabled", true)) {
                            String particleType = plugin.getConfig().getString("Economy.Apply-Particles." + rarity + ".Type", "VILLAGER_HAPPY");
                            int particleCount = plugin.getConfig().getInt("Economy.Apply-Particles." + rarity + ".Count", 15);

                            try {
                                Particle particle = Particle.valueOf(particleType);
                                player.getWorld().spawnParticle(particle, player.getLocation().add(0, 1, 0), particleCount, 0.5, 0.5, 0.5, 0.1);
                            } catch (IllegalArgumentException e) {
                                // Invalid particle type, use default
                                player.getWorld().spawnParticle(Particle.VILLAGER_HAPPY, player.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
                            }
                        }

                        // Play sound if enabled
                        if (plugin.getConfig().getBoolean("Economy.Apply-Sounds.Enabled", true)) {
                            String soundName = plugin.getConfig().getString("Economy.Apply-Sounds." + rarity + ".Sound", "ENTITY_PLAYER_LEVELUP");
                            float volume = (float) plugin.getConfig().getDouble("Economy.Apply-Sounds." + rarity + ".Volume", 0.5);
                            float pitch = (float) plugin.getConfig().getDouble("Economy.Apply-Sounds." + rarity + ".Pitch", 1.0);

                            try {
                                Sound sound = Sound.valueOf(soundName);
                                player.playSound(player.getLocation(), sound, volume, pitch);
                            } catch (IllegalArgumentException e) {
                                // Invalid sound, use default
                                player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.0f);
                            }
                        }
                    }

                    // Send success message
                    String successMsg = plugin.getMessagesConfig().getString("Enchantment.ApplySuccess", "&aEnchantment applied successfully!");
                    player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + successMsg));
                } else {
                    // No item in hand
                    String noItemMsg = plugin.getMessagesConfig().getString("Enchantment.NoItem", "&cYou must hold an item to apply the enchantment!");
                    player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + noItemMsg));
                }

                // Close the inventory
                player.closeInventory();
            } else if (event.getRawSlot() == cancelSlot) {
                // Cancel button clicked

                // Mark that we're handling the book return here
                bookAlreadyReturned.add(uuid);

                if (plugin.getConfig().getBoolean("Debug", false)) {
                    player.sendMessage(ColorUtils.process("&7[Debug] Cancel button clicked, marked book as being returned."));
                }

                // Get the original book from our stored map
                ItemStack bookToReturn = originalCursorItems.get(uuid);

                if (bookToReturn == null && pendingEnchantments.containsKey(uuid)) {
                    // Fallback to the pending enchantment if original cursor not found
                    bookToReturn = pendingEnchantments.get(uuid).clone();

                    if (plugin.getConfig().getBoolean("Debug", false)) {
                        player.sendMessage(ColorUtils.process("&7[Debug] Using fallback book for return."));
                    }
                }

                // Make sure we have a final reference for the task
                final ItemStack finalBookToReturn = bookToReturn;

                // Close the inventory first
                player.closeInventory();

                // Schedule a task to handle the book return after inventory is closed
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    // Clear cursor first
                    player.setItemOnCursor(null);

                    // Debug message
                    if (plugin.getConfig().getBoolean("Debug", false)) {
                        player.sendMessage(ColorUtils.process("&7[Debug] Returning book to inventory..."));
                    }

                    // Return the book to the player's inventory
                    if (finalBookToReturn != null) {
                        // Try to add the book to the player's inventory
                        HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(finalBookToReturn);

                        if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Attempting to return book: " +
                                finalBookToReturn.getType() + " x" + finalBookToReturn.getAmount()));
                        }

                        if (!leftover.isEmpty()) {
                            // If inventory is full, drop the book at player's location
                            for (ItemStack item : leftover.values()) {
                                player.getWorld().dropItemNaturally(player.getLocation(), item);

                                if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Inventory full, dropped book."));
                                }
                            }
                        } else if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Book returned to inventory successfully."));
                        }
                    } else if (plugin.getConfig().getBoolean("Debug", false)) {
                        player.sendMessage(ColorUtils.process("&7[Debug] No book to return!"));
                    }

                    player.updateInventory();
                }, 2L);

                // Send cancel message
                String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.ApplyCancel", "&cEnchantment application cancelled.");
                player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));
            }
        }
    }

    /**
     * Handle inventory close events
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;

        Player player = (Player) event.getPlayer();
        UUID uuid = player.getUniqueId();

        // Check if this was our combine GUI
        if (pendingCombineBook1.containsKey(uuid) && pendingCombineBook2.containsKey(uuid)) {
            ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("CombineGUI");
            if (config != null) {
                String title = ColorUtils.process(config.getString("Title", "Combine Grimoires?"));

                // Check if this is our GUI by title
                if (event.getView().getTitle().equals(title)) {
                    // Check if the books have already been handled (by confirm/cancel button)
                    if (combineItemsReturned.contains(uuid)) {
                        if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Combine items already handled, skipping return on close."));
                        }
                    } else {
                        // Books haven't been handled yet, so return them now
                        // Get the original book from our stored map
                        ItemStack bookToReturn = originalCursorItems.get(uuid);

                        if (bookToReturn == null) {
                            // Fallback to the first book if original cursor not found
                            bookToReturn = pendingCombineBook1.get(uuid);

                            if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] Using fallback book for return on close."));
                            }
                        }

                        // Make sure we have a final reference for the task
                        final ItemStack finalBookToReturn = bookToReturn != null ? bookToReturn.clone() : null;

                        // Schedule a task to handle the book return after inventory is closed
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            // Clear cursor first
                            player.setItemOnCursor(null);

                            if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] Returning book to inventory after GUI close..."));
                            }

                            // Return the book to the player's inventory
                            if (finalBookToReturn != null) {
                                // Try to add the book to the player's inventory
                                HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(finalBookToReturn);

                                if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Attempting to return book: " +
                                        finalBookToReturn.getType() + " x" + finalBookToReturn.getAmount()));
                                }

                                if (!leftover.isEmpty()) {
                                    // If inventory is full, drop the book at player's location
                                    for (ItemStack item : leftover.values()) {
                                        player.getWorld().dropItemNaturally(player.getLocation(), item);

                                        if (plugin.getConfig().getBoolean("Debug", false)) {
                                            player.sendMessage(ColorUtils.process("&7[Debug] Inventory full, dropped book."));
                                        }
                                    }
                                } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Book returned to inventory successfully after GUI close."));
                                }
                            } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] No book to return on GUI close!"));
                            }

                            player.updateInventory();
                        }, 2L);

                        // Send cancel message
                        String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.CombineCancel", "&#FF6347ɢʀɪᴍᴏɪʀᴇ ᴄᴏᴍʙɪɴᴀᴛɪᴏɴ ᴄᴀɴᴄᴇʟʟᴇᴅ.");
                        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));
                    }
                }
            }

            // Remove all combine data
            pendingCombineBook1.remove(uuid);
            pendingCombineBook2.remove(uuid);
            combineBook1Slot.remove(uuid);
            combineBook2Slot.remove(uuid);
            combineInventories.remove(uuid);
            combineItemsReturned.remove(uuid);
        }

        // Check if this was our confirmation GUI
        if (pendingEnchantments.containsKey(uuid)) {
            ConfigurationSection config = plugin.getGuiConfig().getConfigurationSection("ConfirmationGUI");
            if (config != null) {
                String title = ColorUtils.process(config.getString("Title", "Apply Enchantment?"));

                // Check if this is our GUI by title
                if (event.getView().getTitle().equals(title)) {
                    // Check if the book has already been returned (by cancel button)
                    if (bookAlreadyReturned.contains(uuid)) {
                        if (plugin.getConfig().getBoolean("Debug", false)) {
                            player.sendMessage(ColorUtils.process("&7[Debug] Book already returned, skipping return on close."));
                        }
                    } else {
                        // Book hasn't been returned yet, so return it now
                        // Get the original book from our stored map
                        ItemStack bookToReturn = originalCursorItems.get(uuid);

                        if (bookToReturn == null) {
                            // Fallback to the pending enchantment if original cursor not found
                            bookToReturn = pendingEnchantments.get(uuid);

                            if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] Using fallback book for return on close."));
                            }
                        }

                        // Make sure we have a final reference for the task
                        final ItemStack finalBookToReturn = bookToReturn != null ? bookToReturn.clone() : null;

                        // Schedule a task to handle the book return after inventory is closed
                        Bukkit.getScheduler().runTaskLater(plugin, () -> {
                            // Clear cursor first
                            player.setItemOnCursor(null);

                            if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] Returning book to inventory after GUI close..."));
                            }

                            // Return the book to the player's inventory
                            if (finalBookToReturn != null) {
                                // Try to add the book to the player's inventory
                                HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(finalBookToReturn);

                                if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Attempting to return book: " +
                                        finalBookToReturn.getType() + " x" + finalBookToReturn.getAmount()));
                                }

                                if (!leftover.isEmpty()) {
                                    // If inventory is full, drop the book at player's location
                                    for (ItemStack item : leftover.values()) {
                                        player.getWorld().dropItemNaturally(player.getLocation(), item);

                                        if (plugin.getConfig().getBoolean("Debug", false)) {
                                            player.sendMessage(ColorUtils.process("&7[Debug] Inventory full, dropped book."));
                                        }
                                    }
                                } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                    player.sendMessage(ColorUtils.process("&7[Debug] Book returned to inventory successfully after GUI close."));
                                }
                            } else if (plugin.getConfig().getBoolean("Debug", false)) {
                                player.sendMessage(ColorUtils.process("&7[Debug] No book to return on GUI close!"));
                            }

                            player.updateInventory();
                        }, 2L);

                        // Send cancel message
                        String cancelMsg = plugin.getMessagesConfig().getString("Enchantment.ApplyCancel", "&cEnchantment application cancelled.");
                        player.sendMessage(ColorUtils.process(plugin.getMessagesConfig().getString("General.Prefix", "") + cancelMsg));
                    }
                }
            }

            // Remove all stored data
            pendingEnchantments.remove(uuid);
            targetItems.remove(uuid);
            targetSlots.remove(uuid);
            targetSlotTypes.remove(uuid);
            targetInventories.remove(uuid);
            originalCursorItems.remove(uuid);
            bookAlreadyReturned.remove(uuid);
        }
    }
}
